import React, { useState, useEffect } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabaseClient';
import { createPortal } from 'react-dom';

// Note interface for individual notes
export interface Note {
  id: string;
  lead_id: string;
  info: string;
  created_at: string;
  updated_at: string;
}

// Use the same Lead interface as in LeadsKanban
export interface Lead {
  id: string;
  customer_name: string;
  name?: string;
  email: string;
  phone?: string;
  destination?: string;
  departure_city?: string;
  travel_date?: string;
  adults?: number;
  children?: number;
  infants?: number;
  nights?: number;
  budget_range?: string;
  lead_source?: string;
  status: string;
  priority?: string;
  notes?: string;
  special_requests?: string;
  assigned_to: string;
  created_at: string;
  updated_at?: string;
  package_type?: string;
  lead_number?: string;
  scheduled_to?: string;
  // Array of notes for this lead (populated via JOIN or separate fetch)
  lead_notes?: Note[];
}

interface StatusConfig {
  color: string;
  lightColor: string;
  borderColor: string;
  textColor: string;
  icon: string;
  cardColor: string;
}

interface LeadCardProps {
  lead: Lead;
  isUpdating?: boolean;
  onViewDetails?: (leadId: string) => void;
  onCreateQuote?: (lead: Lead) => void;
  statusConfig?: StatusConfig;
}

const LeadCard: React.FC<LeadCardProps> = ({
  lead,
  isUpdating = false,
  onViewDetails,
  onCreateQuote: _onCreateQuote,
  statusConfig
}) => {
  const navigate = useNavigate();
  const [notes, setNotes] = useState<Note[]>([]);
  const [showNotesTooltip, setShowNotesTooltip] = useState(false);
  const [tooltipAnchor, setTooltipAnchor] = useState<{ top: number; left: number } | null>(null);

  useEffect(() => {
    if (lead.id) {
      loadNotesForLead(lead.id);
    }
  }, [lead.id]);

  const loadNotesForLead = async (leadId: string) => {
    try {
      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('lead_id', leadId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setNotes(data || []);
    } catch (error) {
      console.error('Error loading notes:', error);
    }
  };

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: lead.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    rotate: isDragging ? '2deg' : '0deg',
    scale: isDragging ? 1.02 : 1,
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No date';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return 'Invalid';
    }
  };

  const getPriorityDot = (priority?: string) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return '🔴';
      case 'medium':
        return '🟡';
      default:
        return '🟢';
    }
  };

  const handleCreateQuote = (e: React.MouseEvent) => {
    e.stopPropagation();

    const leadDataForQuote = {
      customerName: lead.customer_name,
      destination: lead.destination || '',
      travelDate: lead.travel_date || '',
      source: lead.lead_source || '',
      email: lead.email,
      phone: lead.phone || '',
      adults: lead.adults || 1,
      children: lead.children || 0,
      infants: lead.infants || 0,
      nights: lead.nights || 1,
      budgetRange: lead.budget_range || '',
      notes: lead.notes || '',
      specialRequests: lead.special_requests || '',
    };

    sessionStorage.setItem('leadDataForQuote', JSON.stringify(leadDataForQuote));
    navigate('/quotes/new');
  };

  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    console.log('[LeadCard] View Details clicked for lead:', lead.id, lead.customer_name);

    if (onViewDetails) {
      onViewDetails(lead.id);
    } else {
      console.warn('[LeadCard] onViewDetails prop is not provided');
    }
  };

  // Enhanced floating card with better spacing
  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`relative transition-all duration-300 ease-in-out mb-1 ${
        isDragging 
          ? 'opacity-85 z-50 rotate-2 scale-105' 
          : 'z-auto hover:scale-102 hover:-translate-y-1'
      }`}
    >
      {/* Enhanced Material Design Card with Floating Effect */}
      <div 
        className={`bg-white rounded-xl p-4 border border-gray-100 relative overflow-hidden transition-all duration-300 ${
          isDragging 
            ? 'shadow-2xl shadow-blue-500/25' 
            : 'shadow-lg hover:shadow-xl hover:shadow-blue-500/10'
        }`}
        style={{
          backgroundColor: statusConfig?.cardColor || '#ffffff',
          borderLeftColor: statusConfig ? statusConfig.color.replace('bg-', '#') : '#e5e7eb',
          borderLeftWidth: '4px',
          minHeight: '130px',
          // Enhanced floating shadows
          boxShadow: isDragging
            ? '0 20px 40px rgba(0,0,0,0.15), 0 10px 20px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.05)'
            : '0 4px 15px rgba(0,0,0,0.1), 0 2px 8px rgba(0,0,0,0.06)',
        }}
      >
        {/* Status Icon with better positioning */}
        {statusConfig && (
          <div className="absolute top-3 right-3 text-sm opacity-70 transition-opacity hover:opacity-100">
            {statusConfig.icon}
          </div>
        )}

        {/* Enhanced loading state */}
        {isUpdating && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-95 z-20 rounded-xl backdrop-blur-sm">
            <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg shadow-md">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
              <span className="text-xs text-gray-600 font-medium">Updating...</span>
            </div>
          </div>
        )}

        {/* Drag handle area with better spacing */}
        <div
          {...listeners}
          className="cursor-move relative z-10 mb-4"
        >
          {/* Header with enhanced styling */}
          <div className="flex justify-between items-start mb-3">
            <h3 className="text-sm font-bold text-gray-800 leading-tight truncate pr-2 flex-1">
              {lead.customer_name}
            </h3>
            <span className="text-sm flex-shrink-0">
              {getPriorityDot(lead.priority)}
            </span>
          </div>

          {/* Enhanced details with better spacing */}
          <div className="space-y-2 text-xs">
            <div className="flex items-center">
              <span className="text-gray-500 w-5 flex-shrink-0">📍</span>
              <span className="text-gray-700 font-medium truncate ml-2">
                {lead.destination || 'No destination'}
              </span>
            </div>

            <div className="flex items-center">
              <span className="text-gray-500 w-5 flex-shrink-0">📅</span>
              <span className="text-gray-700 ml-2">
                {formatDate(lead.travel_date)}
              </span>
            </div>

            {lead.phone && (
              <div className="flex items-center">
                <span className="text-gray-500 w-5 flex-shrink-0">📞</span>
                <span className="text-gray-600 text-xs ml-2 truncate">
                  {lead.phone}
                </span>
              </div>
            )}

            <div className="flex items-center">
              <span className="text-gray-500 w-5 flex-shrink-0">🔗</span>
              <span className="text-gray-600 text-xs ml-2 truncate">
                {lead.lead_source || 'Direct'}
              </span>
            </div>

            {notes.length > 0 && (
              notes.length === 1 ? (
                <div
                  className="flex items-center relative"
                  onMouseEnter={(e) => {
                    if (notes[0].info.length > 50) {
                      const rect = e.currentTarget.getBoundingClientRect();
                      setTooltipAnchor({ top: rect.top, left: rect.left });
                      setShowNotesTooltip(true);
                    }
                  }}
                  onMouseLeave={() => {
                    setShowNotesTooltip(false);
                    setTooltipAnchor(null);
                  }}
                >
                  <span className="text-gray-500 w-5 flex-shrink-0">📝</span>
                  <span className="text-gray-600 text-xs ml-2 truncate">
                    {notes[0].info}
                  </span>
                  {showNotesTooltip && tooltipAnchor && createPortal(
                    <div
                      className="fixed z-50 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg max-w-xs"
                      style={{
                        top: tooltipAnchor.top - 10,
                        left: tooltipAnchor.left,
                        transform: 'translateY(-100%)',
                      }}
                    >
                      <div className="whitespace-pre-wrap break-words">
                        {notes[0].info}
                      </div>
                    </div>,
                    document.body
                  )}
                </div>
              ) : (
                notes.map((note, index) => (
                  <div key={note.id} className="flex items-center">
                    <span className="text-gray-500 w-5 flex-shrink-0">{index === 0 ? '📝' : ''}</span>
                    <span className="text-gray-600 text-xs ml-2">
                      {note.info}
                    </span>
                  </div>
                ))
              )
            )}
          </div>
        </div>

        {/* Enhanced Action Buttons with reduced size */}
        <div className="flex space-x-2 pt-3 border-t border-gray-100 border-opacity-60 relative z-20">
          <button
            type="button"
            onClick={handleViewDetails}
            className="flex-1 px-2 py-1 text-xs bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg border border-gray-200 hover:border-gray-300 font-medium transition-all duration-200 flex items-center justify-center space-x-1 hover:shadow-md"
            style={{ pointerEvents: 'auto' }}
          >
            <span>👁️</span>
            <span>View</span>
          </button>
          <button
            type="button"
            onClick={handleCreateQuote}
            className={`flex-1 px-2 py-1 text-xs text-white rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-1 hover:shadow-md ${
              statusConfig
                ? `${statusConfig.color} hover:opacity-90`
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
            style={{ pointerEvents: 'auto' }}
          >
            <span>📋</span>
            <span>Quote</span>
          </button>
          {/* WhatsApp Button */}
          {lead.phone && (
            <button
              type="button"
              onClick={() => {
                let phone = (lead.phone ?? '').trim();
                phone = phone.replace(/[-\s()]/g, '');
                let formattedPhone = '';
                let countryCode = '';
                if (phone.startsWith('+')) {
                  // Extract country code (first 1-3 digits after '+')
                  const match = phone.match(/^\+(\d{1,3})(\d{6,})$/);
                  if (match) {
                    countryCode = match[1];
                    formattedPhone = countryCode + match[2];
                  } else {
                    formattedPhone = phone.replace(/[^\d+]/g, '').replace('+', '');
                  }
                } else if (/^\d{10}$/.test(phone)) {
                  countryCode = '91';
                  formattedPhone = countryCode + phone;
                } else {
                  formattedPhone = phone;
                }
                if (!/^\d{11,15}$/.test(formattedPhone)) {
                  alert('Invalid phone number for WhatsApp. Please check the number format.');
                  return;
                }
                // Status-based message
                let message = '';
                switch (lead.status.toUpperCase()) {
                  case 'FOLLOW-UP':
                  case 'FOLLOW UP':
                    message = `Hi ${lead.customer_name},\n\nJust following up regarding your trip inquiry. Do you have any update or questions?\n\n- TripXplo Team`;
                    break;
                  case 'QUOTE SENT':
                    message = `Hi ${lead.customer_name},\n\nWe have sent you a quote for your trip. Please check and let us know if you have any questions!\n\n- TripXplo Team`;
                    break;
                  case 'BOOKED WITH US':
                    message = `Hi ${lead.customer_name},\n\nThank you for booking your trip with us! We look forward to serving you.\n\n- TripXplo Team`;
                    break;
                  default:
                    message = `Hi ${lead.customer_name},\n\nThank you for your interest in TripXplo. Let us know if you have any questions!\n\n- TripXplo Team`;
                }
                const encodedMsg = encodeURIComponent(message);
                const waUrl = `https://wa.me/${formattedPhone}?text=${encodedMsg}`;
                window.open(waUrl, '_blank');
              }}
              className="flex-1 px-2 py-1 text-xs text-white rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-1 hover:shadow-md bg-green-600 hover:bg-green-700"
              style={{ pointerEvents: 'auto' }}
              title="Send WhatsApp Message"
            >
              <span>WhatsApp</span>
            </button>
          )}
        </div>

        {/* Enhanced Material Design Accent with gradient */}
        <div 
          className="absolute bottom-0 left-0 h-1 rounded-b-xl opacity-40"
          style={{
            width: '100%',
            background: statusConfig 
              ? `linear-gradient(90deg, ${statusConfig.color.replace('bg-', '#')}, ${statusConfig.color.replace('bg-', '#')}80)`
              : 'linear-gradient(90deg, #3b82f6, #3b82f680)',
          }}
        />

        {/* Subtle glow effect on hover */}
        <div 
          className="absolute inset-0 rounded-xl opacity-0 hover:opacity-5 transition-opacity duration-300 pointer-events-none"
          style={{
            background: statusConfig 
              ? `radial-gradient(circle at center, ${statusConfig.color.replace('bg-', '#')}, transparent)`
              : 'radial-gradient(circle at center, #3b82f6, transparent)',
          }}
        />
      </div>
    </div>
  );
};

export default LeadCard;
