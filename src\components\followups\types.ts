export interface Followup {
  id: number;
  customer_name: string;
  customer_phone?: string;
  customer_email?: string;
  quotes: Quote | null;
  hotel_status: string;
  cab_status: string;
  flight_train_status: string;
  transportation_status: string;
  cab_payment?: number;
  quote_id?: string;
  hotel_details?: string; // Hotel Name
  hotel_full_amount?: number;
  hotel_advance_amount?: number;
  hotel_full_amount_paid?: number;
  cab_name?: string;
  cab_full_amount?: number;
  cab_advance_amount?: number;
  cab_full_amount_paid?: number;
  flight_train_quote_amount?: number;
  flight_train_full_amount_paid?: number;
  transportation_quote_amount?: number;
  transportation_full_amount_paid?: number;
  notes?: string;
  profit?: number;
  marketing_amount?: number;
  total_cost?: number;
  total_paid?: number;
  add_on_amount?: number;
  subtotal?: number;
  commission_amount?: number;
  gst_amount?: number;
}

export interface Quote {
  id: string;
  quote_id: string;
  customer_name: string;
  customer_phone: string;
  customer_email: string;
  package_name?: string;
  destination?: string;
  created_at?: string;
  is_draft?: boolean;
  trip_duration?: string;
  family_type?: string;
  validity_date?: string;
  travel_date?: string;
  subtotal?: number;
  total_cost?: number;
  no_of_persons?: number;
  extra_adults?: number;
  children?: number;
  commission_rate?: number;
  costs?: Costs;
}

export interface QuoteListItem {
  id: string;
  package_name: string;
  customer_name: string;
  customer_phone?: string;
  customer_email?: string;
  destination: string;
  created_at: string;
  is_draft: boolean;
  trip_duration?: string;
  family_type?: string;
  validity_date?: string;
  subtotal?: number;
  total_cost?: number;
  no_of_persons?: number;
  extra_adults?: number;
  children?: number;
}

export interface HotelDetail {
  name: string;
  price: number;
  advance: number;
  full_amount_paid: number;
  status: string;
}

export interface Costs {
  id?: string;
  quote_id?: string;
  transportation?: number;
  cab_sightseeing?: number;
  train_cost?: number;
  ferry_cost?: number;
  marketing?: number;
  gst?: number;
  gst_amount?: number;
  add_on_activity?: number;
  commission?: number;
  commission_amount?: number;
  subtotal?: number;
}

export interface HotelRow {
  hotel_name: string;
  stay_price: number;
  price?: number; // Keep price optional for backward compatibility if needed
}
