import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, RotateCcw, Users, Trash2, ChevronDown, ChevronUp } from 'lucide-react';
import { getQuoteClient } from '../../lib/supabaseManager';
import { QuoteListItem } from './types';

const SelectQuote: React.FC = () => {
  const navigate = useNavigate();
  const [supabase, setSupabase] = useState<any>(null);
  const [isClientLoading, setIsClientLoading] = useState(true);
  const [savedQuotes, setSavedQuotes] = useState<QuoteListItem[]>([]);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [collapsedCustomers, setCollapsedCustomers] = useState<Set<string>>(new Set());

  useEffect(() => {
    const initSupabase = async () => {
      try {
        setIsClientLoading(true);
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Quote Supabase client:', error);
      } finally {
        setIsClientLoading(false);
      }
    };

    initSupabase();
  }, []);

  const fetchSavedQuotes = async () => {
    if (!supabase) return;
    setIsLoadingQuotes(true);
    try {
      const { data, error } = await supabase
        .from('quotes')
        .select('id, package_name, customer_name, customer_phone, customer_email, destination, created_at, is_draft, trip_duration, family_type, validity_date, subtotal, total_cost, no_of_persons, extra_adults, children')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching saved quotes:', error);
        setSavedQuotes([]);
        return;
      }
      setSavedQuotes(data || []);
    } catch (error) {
      console.error('Exception fetching saved quotes:', error);
      setSavedQuotes([]);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  useEffect(() => {
    if (supabase) {
      fetchSavedQuotes();
    }
  }, [supabase]);

  const handleSelectQuote = (quote: QuoteListItem) => {
    const queryParams = new URLSearchParams();
    queryParams.set('quoteId', quote.id);
    if (quote.customer_name) queryParams.set('customerName', quote.customer_name);
    if (quote.customer_phone) queryParams.set('customerPhone', quote.customer_phone);
    if (quote.customer_email) queryParams.set('customerEmail', quote.customer_email);

    navigate(`/followups/create?${queryParams.toString()}`);
  };

  const deleteQuote = async (quoteId: string) => {
    if (!supabase) return;
    setIsLoadingQuotes(true);
    try {
      const { error } = await supabase.from('quotes').delete().eq('id', quoteId);
      if (error) throw error;

      await fetchSavedQuotes();
      alert('Quote deleted successfully');
    } catch (error) {
      console.error('Error deleting quote:', error);
      alert('Failed to delete quote');
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  const filteredQuotes = savedQuotes.filter((quote) => {
    const term = searchTerm.toLowerCase();
    return (
      (quote.destination || '').toLowerCase().includes(term) ||
      (quote.customer_name || '').toLowerCase().includes(term) ||
      (quote.customer_phone || '').toLowerCase().includes(term) ||
      (quote.customer_email || '').toLowerCase().includes(term)
    );
  });

  useEffect(() => {
    setCurrentPage(1);
    setCollapsedCustomers(new Set());
  }, [searchTerm, itemsPerPage]);

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
  };

  const formatPax = (quote: any) => {
    const adults = (quote.no_of_persons || 0) + (quote.extra_adults || 0);
    const children = quote.children || 0;
    return `${adults} Adult${adults > 1 ? 's' : ''}${children ? `, ${children} Child${children > 1 ? 'ren' : ''}` : ''}`;
  };

  const formatDateDisplay = (dateString: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const day = date.getDate().toString().padStart(2, '0');
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const groupQuotesByCustomer = (quotes: QuoteListItem[]) => {
    const grouped: { [key: string]: QuoteListItem[] } = {};
    quotes.forEach(quote => {
      const key = quote.customer_phone || quote.customer_name || 'Unknown Customer';
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(quote);
    });
    return grouped;
  };

  const groupedQuotes = groupQuotesByCustomer(filteredQuotes);
  const customerKeys = Object.keys(groupedQuotes);
  const totalPages = Math.ceil(customerKeys.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, customerKeys.length);
  const paginatedCustomerKeys = customerKeys.slice(startIndex, endIndex);

  const toggleCustomerCollapse = (customerKey: string) => {
    const newCollapsed = new Set(collapsedCustomers);
    if (newCollapsed.has(customerKey)) {
      newCollapsed.delete(customerKey);
    } else {
      newCollapsed.add(customerKey);
    }
    setCollapsedCustomers(newCollapsed);
  };

  if (isClientLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#00B69B] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Quotes</h2>
          <p className="text-gray-600">Please wait while we connect to the database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-8">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow p-4 sm:p-6">
          <div className="flex justify-between items-center mb-4 sm:mb-6">
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">Select a Quote</h2>
            <button onClick={() => fetchSavedQuotes()} className="px-4 py-2 flex items-center gap-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
              <RotateCcw size={14} />
              Refresh
            </button>
          </div>
          <div className="mb-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search by customer name, phone, email, or destination..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              )}
            </div>
          </div>
          {isLoadingQuotes ? (
            <div className="flex justify-center py-16"><div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#00B69B]"></div></div>
          ) : (
            <>
              {filteredQuotes.length === 0 ? (
                <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No Saved Quotes Found</h3>
                </div>
              ) : (
                <div className="space-y-6">
                  {paginatedCustomerKeys.map((customerKey) => {
                    const customerQuotes = groupedQuotes[customerKey];
                    const firstQuote = customerQuotes[0];
                    const customerName = firstQuote.customer_name || 'Unknown Customer';
                    const customerPhone = firstQuote.customer_phone;
                    const customerEmail = firstQuote.customer_email;
                    const isCollapsed = collapsedCustomers.has(customerKey);

                    return (
                      <div key={customerKey} className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 cursor-pointer" onClick={() => toggleCustomerCollapse(customerKey)}>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-4">
                              <button className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors">
                                {isCollapsed ? <ChevronDown size={16} className="text-blue-600" /> : <ChevronUp size={16} className="text-blue-600" />}
                              </button>
                              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                                <Users size={20} className="text-blue-600" />
                              </div>
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900">{customerName}</h3>
                                <div className="flex items-center space-x-4 text-sm text-gray-600">
                                  {customerPhone && <span className="flex items-center">📱 {customerPhone}</span>}
                                  {customerEmail && <span className="flex items-center">📧 {customerEmail}</span>}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        {!isCollapsed && (
                          <div className="divide-y divide-gray-100">
                            {customerQuotes.map((quote) => (
                              <div key={quote.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                                <div className="flex justify-between items-center">
                                  <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4">
                                    <div>
                                      <div className="font-medium text-gray-900">{quote.package_name || 'Untitled'}</div>
                                      <div className="text-sm text-gray-500">{quote.trip_duration || '-'}</div>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium text-gray-700">{quote.destination || 'N/A'}</div>
                                      <div className="text-xs text-gray-500">{quote.family_type || '-'}</div>
                                    </div>
                                    <div>
                                      <div className="text-sm text-gray-700">₹{quote.total_cost?.toLocaleString() || '0'}</div>
                                      <div className="text-xs text-gray-500">{formatPax(quote)}</div>
                                    </div>
                                    <div>
                                      <div className="text-sm text-gray-700">Valid until</div>
                                      <div className="text-sm font-medium text-blue-600">{formatDateDisplay(quote.validity_date || '')}</div>
                                    </div>
                                    <div>
                                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${quote.is_draft ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                                        {quote.is_draft ? 'Draft' : 'Final'}
                                      </span>
                                      <div className="text-xs text-gray-500 mt-1">{formatDateDisplay(quote.created_at)}</div>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2 ml-4">
                                    <button
                                      onClick={() => handleSelectQuote(quote)}
                                      className="px-3 py-1 bg-[#00B69B] text-white rounded hover:bg-[#008577] transition-colors text-sm"
                                    >
                                      Select
                                    </button>
                                    <button
                                      onClick={() => deleteQuote(quote.id)}
                                      className="p-1.5 bg-red-50 text-red-600 hover:bg-red-100 rounded-md transition-colors"
                                      title="Delete quote"
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
              {filteredQuotes.length > 0 && totalPages > 1 && (
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600">
                    Showing <span className="font-semibold">{startIndex + 1}</span> to <span className="font-semibold">{endIndex}</span> of <span className="font-semibold">{customerKeys.length}</span> customers
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <label htmlFor="itemsPerPage" className="text-sm font-medium text-gray-700">Rows per page:</label>
                      <select
                        id="itemsPerPage"
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                        className="px-2 py-1 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      >
                        <option value={5}>5</option>
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={50}>50</option>
                      </select>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                      >
                        Previous
                      </button>
                      <span className="text-sm text-gray-700">
                        Page {currentPage} of {totalPages}
                      </span>
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SelectQuote;
