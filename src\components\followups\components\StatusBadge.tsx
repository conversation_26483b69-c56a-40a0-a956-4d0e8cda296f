import React from 'react';
import { CheckCircle, XCircle, Loader, MinusCircle, Info, Clock } from 'lucide-react';
import { getStatusStyle } from '../utils';

interface StatusBadgeProps {
  status: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const { bgColor, textColor, icon } = getStatusStyle(status);

  const getIcon = () => {
    switch (icon) {
      case 'CheckCircle': return <CheckCircle className="w-4 h-4" />;
      case 'XCircle': return <XCircle className="w-4 h-4" />;
      case 'Loader': return <Loader className="w-4 h-4 animate-spin" />;
      case 'MinusCircle': return <MinusCircle className="w-4 h-4" />;
      case 'Info': return <Info className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
      {getIcon()}
      <span>{status || 'N/A'}</span>
    </div>
  );
};

export default StatusBadge;
