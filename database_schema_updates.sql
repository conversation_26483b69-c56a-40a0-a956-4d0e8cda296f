-- Database Schema Updates for TripXplo CRM Quote Enhancements
-- This file contains the SQL statements to create unified header_rows table for storing headers and their associated rows

-- =====================================================
-- 1. Unified Header Rows Table
-- =====================================================
-- This table stores both headers and their associated rows in a unified structure
-- Each entry can be either a header (with header_title) or a row (with parent_header_id)
CREATE TABLE IF NOT EXISTS header_rows (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id VARCHAR(50) NOT NULL, -- Using VARCHAR to match existing quote package_id format

    -- Header information (populated when entry_type = 'header')
    header_title TEXT,
    header_order INTEGER DEFAULT 0,
 
    -- Row information (populated when entry_type = 'row')
    parent_header_id UUID, -- References the header this row belongs to
    description TEXT,
    no_unit DECIMAL(10,2) DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0,
    igst_percentage DECIMAL(5,2) DEFAULT 0 CHECK (igst_percentage IN (0, 5, 12, 18, 28)),
    gst_amount DECIMAL(12,2) DEFAULT 0,
    calculated_amount DECIMAL(12,2) DEFAULT 0,
    row_order INTEGER DEFAULT 0,

    -- Common fields
    entry_type VARCHAR(10) NOT NULL CHECK (entry_type IN ('header', 'row')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Foreign key constraint for parent header relationship
    CONSTRAINT fk_header_rows_parent_header FOREIGN KEY (parent_header_id) REFERENCES header_rows(id) ON DELETE CASCADE,

    -- Ensure header entries have header_title and rows have parent_header_id
    CONSTRAINT check_header_fields CHECK (
        (entry_type = 'header' AND header_title IS NOT NULL AND parent_header_id IS NULL) OR
        (entry_type = 'row' AND parent_header_id IS NOT NULL AND header_title IS NULL)
    )
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_header_rows_quote_id ON header_rows (quote_id);
CREATE INDEX IF NOT EXISTS idx_header_rows_type ON header_rows (quote_id, entry_type);
CREATE INDEX IF NOT EXISTS idx_header_rows_parent ON header_rows (parent_header_id);
CREATE INDEX IF NOT EXISTS idx_header_rows_order ON header_rows (quote_id, entry_type, header_order, row_order);

-- Add RLS policies for header_rows
ALTER TABLE header_rows ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view header rows" ON header_rows
    FOR SELECT USING (true);

CREATE POLICY "Users can insert header rows" ON header_rows
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update header rows" ON header_rows
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete header rows" ON header_rows
    FOR DELETE USING (true);

-- Add gst_amount column if it doesn't exist (for existing tables)
ALTER TABLE header_rows ADD COLUMN IF NOT EXISTS gst_amount DECIMAL(12,2) DEFAULT 0;

-- Add comments for documentation
COMMENT ON TABLE header_rows IS 'Unified table storing both headers and their associated rows for quotes';
COMMENT ON COLUMN header_rows.quote_id IS 'Reference to the quote package_id';
COMMENT ON COLUMN header_rows.entry_type IS 'Type of entry: header or row';
COMMENT ON COLUMN header_rows.header_title IS 'Title of the header (only for header entries)';
COMMENT ON COLUMN header_rows.header_order IS 'Order of headers within a quote';
COMMENT ON COLUMN header_rows.parent_header_id IS 'Reference to parent header (only for row entries)';
COMMENT ON COLUMN header_rows.description IS 'Description of the item/service (only for row entries)';
COMMENT ON COLUMN header_rows.no_unit IS 'Number of units (only for row entries)';
COMMENT ON COLUMN header_rows.price IS 'Price per unit (only for row entries)';
COMMENT ON COLUMN header_rows.igst_percentage IS 'IGST percentage: 0%, 5%, 12%, 18%, 28% (only for row entries)';
COMMENT ON COLUMN header_rows.gst_amount IS 'GST amount: (no_unit × price × igst_percentage) / 100 (only for row entries)';
COMMENT ON COLUMN header_rows.calculated_amount IS 'Calculated amount: (no_unit × price) + gst_amount (only for row entries)';
COMMENT ON COLUMN header_rows.row_order IS 'Order of rows within a header (only for row entries)';

-- =====================================================
-- 2. Invoice Storage Table (for storing invoice data)
-- =====================================================
-- This table stores generated invoice data for future reference
CREATE TABLE IF NOT EXISTS quote_invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id VARCHAR(50) NOT NULL, -- Using VARCHAR to match existing quote package_id format
    invoice_number TEXT NOT NULL UNIQUE,
    invoice_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    igst_total DECIMAL(12,2) NOT NULL DEFAULT 0,
    grand_total DECIMAL(12,2) NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'INR',
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quote_invoices_quote_id ON quote_invoices (quote_id);
CREATE INDEX IF NOT EXISTS idx_quote_invoices_number ON quote_invoices (invoice_number);
CREATE INDEX IF NOT EXISTS idx_quote_invoices_date ON quote_invoices (invoice_date);

-- Add RLS policies for quote_invoices
ALTER TABLE quote_invoices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view quote invoices" ON quote_invoices
    FOR SELECT USING (true);

CREATE POLICY "Users can insert quote invoices" ON quote_invoices
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update quote invoices" ON quote_invoices
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete quote invoices" ON quote_invoices
    FOR DELETE USING (true);

-- Add comments for documentation
COMMENT ON TABLE quote_invoices IS 'Stores generated invoice data for quotes';
COMMENT ON COLUMN quote_invoices.quote_id IS 'Reference to the quote package_id';
COMMENT ON COLUMN quote_invoices.invoice_number IS 'Unique invoice number';
COMMENT ON COLUMN quote_invoices.invoice_date IS 'Date when invoice was generated';
COMMENT ON COLUMN quote_invoices.due_date IS 'Payment due date';
COMMENT ON COLUMN quote_invoices.subtotal IS 'Subtotal before IGST';
COMMENT ON COLUMN quote_invoices.igst_total IS 'Total IGST amount';
COMMENT ON COLUMN quote_invoices.grand_total IS 'Final total amount including IGST';
COMMENT ON COLUMN quote_invoices.currency IS 'Currency code (INR, USD, etc.)';
COMMENT ON COLUMN quote_invoices.status IS 'Invoice status (draft, sent, paid, cancelled)';

-- =====================================================
-- 3. Functions for automatic amount calculation
-- =====================================================
-- Function to automatically calculate amount when row data changes
CREATE OR REPLACE FUNCTION calculate_header_row_amount()
RETURNS TRIGGER AS $$
BEGIN
    -- Only calculate for row entries, not headers
    IF NEW.entry_type = 'row' THEN
        -- Calculate GST amount: (no_unit × price × igst_percentage) / 100
        NEW.gst_amount := (NEW.no_unit * NEW.price * NEW.igst_percentage) / 100;
        -- Calculate total amount: (no_unit × price) + GST amount
        NEW.calculated_amount := (NEW.no_unit * NEW.price) + NEW.gst_amount;
    END IF;
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically calculate amount
CREATE TRIGGER trigger_calculate_header_row_amount
    BEFORE INSERT OR UPDATE ON header_rows
    FOR EACH ROW
    EXECUTE FUNCTION calculate_header_row_amount();

-- =====================================================
-- 4. Update triggers for timestamps
-- =====================================================
-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updating timestamps
CREATE TRIGGER trigger_update_header_rows_updated_at
    BEFORE UPDATE ON header_rows
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_quote_invoices_updated_at
    BEFORE UPDATE ON quote_invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. Sample Data and Testing
-- =====================================================
-- Insert sample data for testing (optional)
/*
-- Example usage:
-- Insert a header
INSERT INTO header_rows (quote_id, entry_type, header_title, header_order) VALUES
('PKG-123456789', 'header', 'Transportation Services', 1);

-- Get the header ID for inserting rows
-- INSERT INTO header_rows (quote_id, entry_type, parent_header_id, description, no_unit, price, igst_percentage, row_order) VALUES
-- ('PKG-123456789', 'row', 'header-id-from-above', 'Airport Transfer', 2, 1000.00, 18, 1),
-- ('PKG-123456789', 'row', 'header-id-from-above', 'Local Sightseeing', 1, 500.00, 12, 2);
*/

-- =====================================================
-- 6. Verification Queries
-- =====================================================
-- Use these queries to verify the tables were created successfully
/*
SELECT 'header_rows table created' as status, COUNT(*) as record_count FROM header_rows;
SELECT 'quote_invoices table created' as status, COUNT(*) as record_count FROM quote_invoices;

-- Query to see headers and their rows grouped together
SELECT
    hr.quote_id,
    hr.entry_type,
    hr.header_title,
    hr.description,
    hr.no_unit,
    hr.price,
    hr.igst_percentage,
    hr.calculated_amount,
    hr.header_order,
    hr.row_order
FROM header_rows hr
WHERE hr.quote_id = 'your-quote-id-here'
ORDER BY hr.header_order, hr.entry_type DESC, hr.row_order;
*/
