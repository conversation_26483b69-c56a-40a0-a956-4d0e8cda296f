import React, { useState, useEffect } from 'react';
import { Search, Filter, Download, Mail, Eye, Edit, ChevronDown, ChevronUp, FileText, Send, CheckCircle, XCircle, Clock, MoreHorizontal, RefreshCw } from 'lucide-react';
import { getQuoteClient } from '../lib/supabaseManager';
import { formatPrice } from '../quotes/utils/formatters';
import { generateInvoicePreview, downloadInvoice, generateInvoicePreviewUrl, HeaderWithRows, InvoiceData } from '../quotes/utils/invoiceGenerator';
import emailjs from '@emailjs/browser';
import { Invoice } from '../types/invoice';
import InvoiceDetailModal from '../components/InvoiceDetailModal';

// Email configuration (same as Quotes.tsx)
const EMAIL_CONFIG = {
  SERVICE_ID: 'service_f2y5i5p',
  TEMPLATE_ID: 'template_2r6r3ai',
  PUBLIC_KEY: 'OOY-mqlCSb0wCaK4k',
  USER_ID: 'OOY-mqlCSb0wCaK4k'
};

// Initialize EmailJS
emailjs.init("YujCzV0dd0QB3l330");

interface FilterState {
  status: string;
  dateRange: {
    start: string;
    end: string;
  };
  search: string;
}

const InvoiceManagement: React.FC = () => {
  const [supabase, setSupabase] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [selectedInvoices, setSelectedInvoices] = useState<Set<string>>(new Set());
  const [showBulkStatusUpdate, setShowBulkStatusUpdate] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof Invoice>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Filter states
  const [filters, setFilters] = useState<FilterState>({
    status: 'all',
    dateRange: {
      start: '',
      end: ''
    },
    search: ''
  });

  // Loading states for various operations
  const [loadingStates, setLoadingStates] = useState<{
    [key: string]: boolean;
  }>({});

  // Modal states
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  // Initialize Supabase client
  useEffect(() => {
    const initSupabase = async () => {
      try {
        setIsLoading(true);
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initSupabase();
  }, []);

  // Fetch invoices from database
  const fetchInvoices = async () => {
    if (!supabase) return;

    try {
      setIsRefreshing(true);

      // Fetch invoices with related quote data
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('quote_invoices')
        .select('*')
        .order('created_at', { ascending: false });

      if (invoicesError) {
        console.error('Error fetching invoices:', invoicesError);
        return;
      }

      // Fetch related quote data for each invoice
      const invoicesWithQuoteData = await Promise.all(
        (invoicesData || []).map(async (invoice: any) => {
          const { data: quoteData, error: quoteError } = await supabase
            .from('quotes')
            .select('customer_name, customer_email, customer_phone, destination, package_name')
            .eq('package_id', invoice.quote_id)
            .single();

          if (quoteError) {
            console.warn(`Could not fetch quote data for invoice ${invoice.invoice_number}:`, quoteError);
          }

          return {
            ...invoice,
            customer_name: quoteData?.customer_name || 'Unknown Customer',
            customer_email: quoteData?.customer_email || '',
            customer_phone: quoteData?.customer_phone || '',
            destination: quoteData?.destination || '',
            package_name: quoteData?.package_name || 'Travel Package'
          };
        })
      );

      setInvoices(invoicesWithQuoteData);
    } catch (error) {
      console.error('Error in fetchInvoices:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Load invoices when supabase client is ready
  useEffect(() => {
    if (supabase) {
      fetchInvoices();
    }
  }, [supabase]);

  // Apply filters and search
  useEffect(() => {
    let filtered = [...invoices];

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(invoice => invoice.status === filters.status);
    }

    // Apply date range filter
    if (filters.dateRange.start) {
      filtered = filtered.filter(invoice =>
        new Date(invoice.invoice_date) >= new Date(filters.dateRange.start)
      );
    }
    if (filters.dateRange.end) {
      filtered = filtered.filter(invoice =>
        new Date(invoice.invoice_date) <= new Date(filters.dateRange.end)
      );
    }

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(invoice =>
        invoice.invoice_number.toLowerCase().includes(searchLower) ||
        invoice.customer_name?.toLowerCase().includes(searchLower) ||
        invoice.customer_email?.toLowerCase().includes(searchLower) ||
        invoice.destination?.toLowerCase().includes(searchLower) ||
        invoice.package_name?.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    setFilteredInvoices(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [invoices, filters, sortField, sortDirection]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentInvoices = filteredInvoices.slice(startIndex, endIndex);

  // Status badge component
  const StatusBadge: React.FC<{ status: Invoice['status'] }> = ({ status }) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', icon: Clock },
      sent: { color: 'bg-blue-100 text-blue-800', icon: Send },
      paid: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle }
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Status update dropdown component
  const StatusUpdateDropdown: React.FC<{
    invoice: Invoice;
    onStatusUpdate: (invoiceId: string, newStatus: Invoice['status']) => void;
  }> = ({ invoice, onStatusUpdate }) => {
    const [isOpen, setIsOpen] = useState(false);

    // Define valid status transitions
    const getValidTransitions = (currentStatus: Invoice['status']): Invoice['status'][] => {
      switch (currentStatus) {
        case 'draft':
          return ['sent', 'cancelled'];
        case 'sent':
          return ['paid', 'cancelled'];
        case 'paid':
          return []; // Paid invoices cannot be changed
        case 'cancelled':
          return ['draft']; // Can reactivate cancelled invoices
        default:
          return [];
      }
    };

    const validTransitions = getValidTransitions(invoice.status);

    if (validTransitions.length === 0) {
      return null;
    }

    const statusLabels = {
      draft: 'Draft',
      sent: 'Sent',
      paid: 'Paid',
      cancelled: 'Cancelled'
    };

    const handleStatusChange = (newStatus: Invoice['status']) => {
      if (newStatus !== invoice.status) {
        // Show confirmation for certain transitions
        if (newStatus === 'cancelled') {
          const confirmed = confirm(`Are you sure you want to cancel invoice ${invoice.invoice_number}?`);
          if (!confirmed) return;
        } else if (newStatus === 'paid') {
          const confirmed = confirm(`Mark invoice ${invoice.invoice_number} as paid?`);
          if (!confirmed) return;
        }

        onStatusUpdate(invoice.id, newStatus);
      }
      setIsOpen(false);
    };

    return (
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-1 text-gray-400 hover:text-gray-600 rounded"
          title="Update Status"
        >
          <MoreHorizontal className="w-4 h-4" />
        </button>

        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />

            {/* Dropdown */}
            <div className="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg z-20 border border-gray-200">
              <div className="py-1">
                <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100">
                  Update Status
                </div>
                {validTransitions.map((status) => (
                  <button
                    key={status}
                    onClick={() => handleStatusChange(status)}
                    className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {statusLabels[status]}
                  </button>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  // Handle sort
  const handleSort = (field: keyof Invoice) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Set loading state for specific operations
  const setLoadingState = (key: string, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [key]: loading }));
  };

  // Handle invoice selection
  const handleInvoiceSelection = (invoiceId: string, selected: boolean) => {
    const newSelection = new Set(selectedInvoices);
    if (selected) {
      newSelection.add(invoiceId);
    } else {
      newSelection.delete(invoiceId);
    }
    setSelectedInvoices(newSelection);
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedInvoices(new Set(currentInvoices.map(invoice => invoice.id)));
    } else {
      setSelectedInvoices(new Set());
    }
  };

  // Update invoice status
  const updateInvoiceStatus = async (invoiceId: string, newStatus: Invoice['status']) => {
    if (!supabase) return;

    try {
      setLoadingState(`status-${invoiceId}`, true);

      const { error } = await supabase
        .from('quote_invoices')
        .update({ status: newStatus })
        .eq('id', invoiceId);

      if (error) {
        console.error('Error updating invoice status:', error);
        alert('Failed to update invoice status');
        return;
      }

      // Update local state
      setInvoices(prev => prev.map(invoice =>
        invoice.id === invoiceId ? { ...invoice, status: newStatus } : invoice
      ));

      alert(`Invoice status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error in updateInvoiceStatus:', error);
      alert('Failed to update invoice status');
    } finally {
      setLoadingState(`status-${invoiceId}`, false);
    }
  };

  // Generate and download PDF for an invoice
  const handleDownloadPDF = async (invoice: Invoice) => {
    try {
      setLoadingState(`pdf-${invoice.id}`, true);

      // Fetch complete invoice data needed for PDF generation
      const invoiceData = await fetchCompleteInvoiceData(invoice);

      if (!invoiceData) {
        alert('Failed to fetch invoice data for PDF generation');
        return;
      }

      // Generate and download PDF using existing utility
      downloadInvoice(invoiceData);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setLoadingState(`pdf-${invoice.id}`, false);
    }
  };

  // Generate and preview PDF for an invoice
  const handlePreviewPDF = async (invoice: Invoice) => {
    try {
      setLoadingState(`preview-${invoice.id}`, true);

      // Fetch complete invoice data needed for PDF generation
      const invoiceData = await fetchCompleteInvoiceData(invoice);

      if (!invoiceData) {
        alert('Failed to fetch invoice data for PDF preview');
        return;
      }

      // Generate and preview PDF using existing utility
      generateInvoicePreview(invoiceData);

    } catch (error) {
      console.error('Error generating PDF preview:', error);
      alert('Failed to generate PDF preview. Please try again.');
    } finally {
      setLoadingState(`preview-${invoice.id}`, false);
    }
  };

  // Fetch complete invoice data for PDF generation
  const fetchCompleteInvoiceData = async (invoice: Invoice): Promise<InvoiceData | null> => {
    if (!supabase) return null;

    try {
      // Fetch quote details
      const { data: quoteData, error: quoteError } = await supabase
        .from('quotes')
        .select('*')
        .eq('package_id', invoice.quote_id)
        .single();

      if (quoteError) {
        console.error('Error fetching quote data:', quoteError);
        return null;
      }

      // Fetch hotel details
      const { data: hotelData, error: hotelError } = await supabase
        .from('hotels')
        .select('*')
        .eq('quote_id', invoice.quote_id);

      if (hotelError) {
        console.warn('Error fetching hotel data:', hotelError);
      }

      // Fetch header rows (service details)
      const { data: headerRowsData, error: headerRowsError } = await supabase
        .from('header_rows')
        .select('*')
        .eq('quote_id', invoice.quote_id)
        .order('header_order')
        .order('row_order');

      if (headerRowsError) {
        console.warn('Error fetching header rows:', headerRowsError);
      }

      // Transform header rows data to match expected format
      const headersWithRows = transformHeaderRowsData(headerRowsData || []);

      // Prepare invoice data for PDF generation
      const invoiceData: InvoiceData = {
        invoiceNumber: invoice.invoice_number,
        invoiceDate: invoice.invoice_date,
        dueDate: invoice.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        customerName: invoice.customer_name || 'Unknown Customer',
        customerEmail: invoice.customer_email || '',
        customerPhone: invoice.customer_phone || '',
        packageName: invoice.package_name || 'Travel Package',
        destination: invoice.destination || '',
        tripDuration: quoteData?.trip_duration || '',
        noOfPersons: quoteData?.no_of_persons || 0,
        children: quoteData?.children || 0,
        infants: quoteData?.infants || 0,
        hotelRows: hotelData || [],
        costs: quoteData?.costs || {},
        grandTotal: invoice.grand_total,
        gstAmount: invoice.igst_total,
        subtotal: invoice.subtotal,
        currency: invoice.currency,
        packageId: invoice.quote_id,
        travelDate: quoteData?.travel_date || '',
        headersWithRows: headersWithRows,
        inclusions: quoteData?.inclusions || [],
        exclusions: quoteData?.exclusions || [],
        commissionAmount: quoteData?.commission_amount || 0
      };

      return invoiceData;
    } catch (error) {
      console.error('Error in fetchCompleteInvoiceData:', error);
      return null;
    }
  };

  // Transform header rows data to match expected format
  const transformHeaderRowsData = (headerRows: any[]): HeaderWithRows[] => {
    // Group by header
    const grouped = headerRows.reduce((acc: any, row: any) => {
      if (!acc[row.header_name]) {
        acc[row.header_name] = {
          id: row.header_id,
          title: row.header_name,
          order: row.header_order,
          rows: []
        };
      }
      acc[row.header_name].rows.push({
        id: row.id,
        description: row.description,
        noUnit: row.quantity,
        price: row.rate,
        igstPercentage: row.gst_percentage || 5,
        calculatedAmount: row.amount,
        rowOrder: row.row_order
      });
      return acc;
    }, {});

    return Object.values(grouped);
  };

  // Handle view details
  const handleViewDetails = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setShowDetailModal(true);
  };

  // Handle send email
  const handleSendEmail = async (invoice: Invoice) => {
    if (!invoice.customer_email) {
      alert('No email address available for this customer');
      return;
    }

    try {
      setLoadingState(`email-${invoice.id}`, true);

      // Generate PDF URL for attachment
      const pdfUrl = await generateInvoicePreviewUrl(await fetchCompleteInvoiceData(invoice) as InvoiceData);

      // Send email using EmailJS
      const templateParams = {
        to_email: invoice.customer_email,
        customer_name: invoice.customer_name,
        invoice_number: invoice.invoice_number,
        invoice_amount: formatPrice(invoice.grand_total),
        pdf_url: pdfUrl,
      };

      await emailjs.send(
        EMAIL_CONFIG.SERVICE_ID,
        EMAIL_CONFIG.TEMPLATE_ID,
        templateParams
      );

      alert('Invoice sent successfully!');
    } catch (error) {
      console.error('Error sending email:', error);
      alert('Failed to send email. Please try again.');
    } finally {
      setLoadingState(`email-${invoice.id}`, false);
    }
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = async (newStatus: Invoice['status']) => {
    if (selectedInvoices.size === 0) return;

    const confirmed = confirm(`Update ${selectedInvoices.size} invoice(s) to ${newStatus}?`);
    if (!confirmed) return;

    try {
      setLoadingState('bulk-status', true);

      for (const invoiceId of selectedInvoices) {
        await updateInvoiceStatus(invoiceId, newStatus);
      }

      setSelectedInvoices(new Set());
      setShowBulkStatusUpdate(false);
      alert(`Successfully updated ${selectedInvoices.size} invoice(s)`);
    } catch (error) {
      console.error('Error in bulk status update:', error);
      alert('Failed to update some invoices');
    } finally {
      setLoadingState('bulk-status', false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00B69B]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Invoice Management</h1>
          <p className="mt-2 text-gray-600">Manage and track all your invoices</p>
        </div>

        {/* Controls */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search invoices..."
                    value={filters.search}
                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-[#00B69B] focus:border-[#00B69B]"
                  />
                </div>
              </div>

              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </button>

              {/* Refresh */}
              <button
                onClick={fetchInvoices}
                disabled={isRefreshing}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Status Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={filters.status}
                      onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                    >
                      <option value="all">All Status</option>
                      <option value="draft">Draft</option>
                      <option value="sent">Sent</option>
                      <option value="paid">Paid</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  {/* Date Range */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={filters.dateRange.start}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, start: e.target.value }
                      }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input
                      type="date"
                      value={filters.dateRange.end}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, end: e.target.value }
                      }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedInvoices.size > 0 && (
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-4 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-700">
                    {selectedInvoices.size} invoice{selectedInvoices.size > 1 ? 's' : ''} selected
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setShowBulkStatusUpdate(true)}
                    disabled={loadingStates['bulk-status']}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#00B69B] hover:bg-[#00A085] disabled:opacity-50"
                  >
                    Update Status
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Status Update Modal */}
        {showBulkStatusUpdate && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Update Invoice Status
                      </h3>
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          onClick={() => handleBulkStatusUpdate('sent')}
                          disabled={loadingStates['bulk-status']}
                          className="w-full text-left px-3 py-2 rounded border hover:bg-gray-50 disabled:opacity-50"
                        >
                          Mark as Sent
                        </button>
                        <button
                          onClick={() => handleBulkStatusUpdate('paid')}
                          disabled={loadingStates['bulk-status']}
                          className="w-full text-left px-3 py-2 rounded border hover:bg-gray-50 disabled:opacity-50"
                        >
                          Mark as Paid
                        </button>
                        <button
                          onClick={() => handleBulkStatusUpdate('cancelled')}
                          disabled={loadingStates['bulk-status']}
                          className="w-full text-left px-3 py-2 rounded border hover:bg-gray-50 disabled:opacity-50"
                        >
                          Mark as Cancelled
                        </button>
                        <button
                          onClick={() => handleBulkStatusUpdate('draft')}
                          disabled={loadingStates['bulk-status']}
                          className="w-full text-left px-3 py-2 rounded border hover:bg-gray-50 disabled:opacity-50"
                        >
                          Mark as Draft
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    onClick={() => setShowBulkStatusUpdate(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Invoice Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedInvoices.size === currentInvoices.length && currentInvoices.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-[#00B69B] focus:ring-[#00B69B]"
                    />
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('invoice_number')}
                  >
                    <div className="flex items-center gap-1">
                      Invoice #
                      {sortField === 'invoice_number' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('customer_name')}
                  >
                    <div className="flex items-center gap-1">
                      Customer
                      {sortField === 'customer_name' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Destination
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('invoice_date')}
                  >
                    <div className="flex items-center gap-1">
                      Date
                      {sortField === 'invoice_date' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('grand_total')}
                  >
                    <div className="flex items-center gap-1">
                      Amount
                      {sortField === 'grand_total' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentInvoices.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                      <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium">No invoices found</p>
                      <p className="text-sm">Try adjusting your filters or create a new invoice from a quote.</p>
                    </td>
                  </tr>
                ) : (
                  currentInvoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedInvoices.has(invoice.id)}
                          onChange={(e) => handleInvoiceSelection(invoice.id, e.target.checked)}
                          className="rounded border-gray-300 text-[#00B69B] focus:ring-[#00B69B]"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{invoice.invoice_number}</div>
                        <div className="text-sm text-gray-500">Quote: {invoice.quote_id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{invoice.customer_name}</div>
                        <div className="text-sm text-gray-500">{invoice.customer_email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{invoice.destination}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(invoice.invoice_date).toLocaleDateString()}
                        </div>
                        {invoice.due_date && (
                          <div className="text-sm text-gray-500">
                            Due: {new Date(invoice.due_date).toLocaleDateString()}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatPrice(invoice.grand_total)} {invoice.currency}
                        </div>
                        <div className="text-sm text-gray-500">
                          Sub: {formatPrice(invoice.subtotal)} {invoice.currency}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <StatusBadge status={invoice.status} />
                          <StatusUpdateDropdown invoice={invoice} onStatusUpdate={updateInvoiceStatus} />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewDetails(invoice)}
                            className="text-[#00B69B] hover:text-[#00A085]"
                            title="View Details"
                          >
                            <Eye className="w-4 h-4" />
                          </button>

                          {invoice.status === 'draft' && (
                            <button
                              onClick={() => alert('Edit functionality will be implemented')}
                              className="text-blue-600 hover:text-blue-800"
                              title="Edit Invoice"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                          )}

                          {invoice.customer_email && (
                            <button
                              onClick={() => handleSendEmail(invoice)}
                              disabled={loadingStates[`email-${invoice.id}`]}
                              className="text-blue-600 hover:text-blue-800 disabled:opacity-50"
                              title="Send Email"
                            >
                              {loadingStates[`email-${invoice.id}`] ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                              ) : (
                                <Mail className="w-4 h-4" />
                              )}
                            </button>
                          )}

                          <button
                            onClick={() => handleDownloadPDF(invoice)}
                            disabled={loadingStates[`pdf-${invoice.id}`]}
                            className="text-green-600 hover:text-green-800 disabled:opacity-50"
                            title="Download PDF"
                          >
                            {loadingStates[`pdf-${invoice.id}`] ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                            ) : (
                              <Download className="w-4 h-4" />
                            )}
                          </button>

                          <button
                            onClick={() => handlePreviewPDF(invoice)}
                            disabled={loadingStates[`preview-${invoice.id}`]}
                            className="text-purple-600 hover:text-purple-800 disabled:opacity-50"
                            title="Preview PDF"
                          >
                            {loadingStates[`preview-${invoice.id}`] ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                            ) : (
                              <FileText className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:text-gray-500 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:text-gray-500 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(endIndex, filteredInvoices.length)}</span> of{' '}
                  <span className="font-medium">{filteredInvoices.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:text-gray-700 disabled:opacity-50"
                  >
                    Previous
                  </button>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === pageNum
                            ? 'z-10 bg-[#00B69B] border-[#00B69B] text-white'
                            : 'bg-white border-gray-300 text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:text-gray-700 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Invoice Detail Modal */}
        {showDetailModal && selectedInvoice && (
          <InvoiceDetailModal
            invoice={selectedInvoice}
            isOpen={showDetailModal}
            onClose={() => {
              setShowDetailModal(false);
              setSelectedInvoice(null);
            }}
            onEdit={() => alert('Edit functionality will be implemented')}
            onPreviewPDF={handlePreviewPDF}
            onDownloadPDF={handleDownloadPDF}
            onSendEmail={handleSendEmail}
          />
        )}
      </div>
    </div>
  );
};

export default InvoiceManagement;