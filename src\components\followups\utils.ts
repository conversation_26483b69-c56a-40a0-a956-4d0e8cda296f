import { Followup, HotelDetail,} from './types';

export const getStatusStyle = (status: string) => {
  if (!status) return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: null };
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes('completed') || lowerStatus.includes('paid') || lowerStatus.includes('booked') || lowerStatus.includes('advance paid') || lowerStatus.includes('flight paid') || lowerStatus.includes('train paid') || lowerStatus.includes('ferry booked') || lowerStatus.includes('bus booked')) {
      return { bgColor: 'bg-green-100', textColor: 'text-green-800', icon: 'CheckCircle' };
  }
  if (lowerStatus.includes('pending')) {
      return { bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', icon: 'Loader' };
  }
  if (lowerStatus.includes('not completed') || lowerStatus.includes('not paid') || lowerStatus.includes('not booked')) {
      return { bgColor: 'bg-red-100', textColor: 'text-red-800', icon: 'XCircle' };
  }
  if (lowerStatus.includes('no flight/train') || lowerStatus.includes('no ferry/bus')) {
      return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: 'MinusCircle' };
  }
  return { bgColor: 'bg-blue-100', textColor: 'text-blue-800', icon: 'Info' };
};

export const parseHotelDetails = (data: Followup): HotelDetail[] => {
  if (!data.hotel_details) return [];
  try {
    const parsed = JSON.parse(data.hotel_details);
    if (Array.isArray(parsed) && parsed.length > 0) {
      return parsed.map(item => ({
        name: item.name || 'Unnamed Hotel',
        price: item.price || 0,
        advance: item.advance || 0,
        full_amount_paid: item.full_amount_paid || 0,
        status: item.status || 'pending',
      }));
    }
  } catch (e) {
    // Fallback for old string format
    return (data.hotel_details || '')
      .split('\n')
      .filter(line => line.trim())
      .map((line, index) => {
        const parts = line.split(': ₹');
        const advance = index === 0 ? (data.hotel_advance_amount || 0) : 0;
        return {
          name: parts[0],
          price: parts.length > 1 ? parseFloat(parts[1].replace(/,/g, '')) : 0,
          advance: advance,
          full_amount_paid: 0,
          status: data.hotel_status || 'pending'
        };
      });
  }
  return [];
};

export const recalculateSubtotal = (
  followup: Followup,
  hotelDetails: HotelDetail[]
): number => {
  const hotelTotal = hotelDetails.reduce((sum, hotel) => sum + (hotel.price || 0), 0);
  const cabTotal = followup.cab_full_amount || 0;
  const flightTrainTotal = followup.flight_train_quote_amount || 0;
  const transportationTotal = followup.transportation_quote_amount || 0;
  const marketingTotal = followup.marketing_amount || 0;

  return hotelTotal + cabTotal + flightTrainTotal + transportationTotal + marketingTotal;
};

export const calculateProfitWithGST = (
  followup: Followup,
  hotelDetails: HotelDetail[],
  _subtotal: number
): {
  profit: number;
  profitBeforeGst: number;
  gst: number;
  commission: number;
  totalExpenses: number;
  breakdown: {
    hotelExpenses: number;
    cabExpenses: number;
    transportationCost: number;
    cabSightseeingCost: number;
    trainCost: number;
    ferryCost: number;
    marketingCost: number;
  }
} => {
  // Use stored values from followups table, fallback to costs table or calculated values
  const costs = followup.quotes?.costs;

  // Use stored values from followups table, fallback to costs table, then parameter
  const commissionAmount = followup.commission_amount || costs?.commission_amount || costs?.commission || 0;
  const gstAmount = followup.gst_amount || costs?.gst_amount || costs?.gst || 0;

  // Get costs from the costs table
  const transportationCost = costs?.transportation || 0;
  const cabSightseeingCost = costs?.cab_sightseeing || 0;
  const trainCost = costs?.train_cost || 0;
  const ferryCost = costs?.ferry_cost || 0;
  const marketingCost = costs?.marketing || 0;

  // Calculate hotel expenses (only paid amounts)
  const hotelExpenses = hotelDetails.reduce((sum, hotel) => sum + (hotel.full_amount_paid || 0), 0);

  // Calculate cab expenses (only paid amounts)
  const cabExpenses = followup.cab_full_amount_paid || 0;

  // Calculate total expenses
  const totalExpenses =
    hotelExpenses +
    cabExpenses +
    transportationCost +
    cabSightseeingCost +
    trainCost +
    ferryCost +
    marketingCost;

  // Calculate totalPaid
  const hotel_full_amount_paid = hotelDetails.reduce((sum, hotel) => sum + (hotel.full_amount_paid || 0), 0);
  const hotel_advance_amount = hotelDetails.reduce((sum, hotel) => sum + (hotel.advance || 0), 0);
  const cab_full_amount_paid = followup.cab_full_amount_paid || 0;
  const cab_advance_amount = followup.cab_advance_amount || 0;
  const flightTrainPaid = followup.flight_train_full_amount_paid || 0;
  const transportPaid = followup.transportation_full_amount_paid || 0;
  const totalPaid = Math.round(
    hotel_full_amount_paid +
    hotel_advance_amount +
    cab_full_amount_paid +
    cab_advance_amount +
    flightTrainPaid +
    transportPaid +
    gstAmount +
    commissionAmount
  );

  // Calculate totalCost
  const totalCost = calculateFollowupTotalCost(followup);

  // Profit calculation: totalCost - totalPaid + commissionAmount
  const profit = totalCost - totalPaid + commissionAmount;

  return {
    profit: Math.round(profit),
    profitBeforeGst: Math.round(profit), // Keep for compatibility
    gst: Math.round(gstAmount),
    commission: Math.round(commissionAmount),
    totalExpenses: Math.round(totalExpenses),
    breakdown: {
      hotelExpenses,
      cabExpenses,
      transportationCost,
      cabSightseeingCost,
      trainCost,
      ferryCost,
      marketingCost,
    }
  };
};

export const calculateTotalQuoteCost = (
  subtotal: number,
  commission: number,
  gst: number
): number => {
  return Math.round(subtotal + commission + gst);
};

export const calculateFollowupTotalCost = (followup: Followup): number => {
  return Math.round(
    (followup.hotel_full_amount || 0) +
    (followup.cab_full_amount || 0) +
    (followup.flight_train_quote_amount || 0) +
    (followup.transportation_quote_amount || 0) +
    (followup.marketing_amount || 0) +
    (followup.add_on_amount || 0) +
    (followup.commission_amount || 0) +
    (followup.gst_amount || 0)
  );
};

/**
 * Format a date string to "4th Sep 2025" format with ordinal suffix
 * @param dateString Date string in 'YYYY-MM-DD' format
 * @returns Formatted date string (e.g., "4th Sep 2025")
 */
export const formatTravelDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear();

    // Add ordinal suffix
    const getOrdinalSuffix = (day: number): string => {
      if (day > 3 && day < 21) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };

    return `${day}${getOrdinalSuffix(day)} ${month} ${year}`;
  } catch (e) {
    console.error('Invalid date format', e);
    return 'N/A';
  }
};
