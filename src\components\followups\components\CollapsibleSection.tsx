import React, { useState } from 'react';
import { ChevronDown, Edit, Save, X } from 'lucide-react';

interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  isEditing: boolean;
  onEdit?: () => void;
  onSave?: () => void;
  onCancel?: () => void;
  showEditButton?: boolean;
  isSectionEditing?: boolean;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  children,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  showEditButton = true,
  isSectionEditing = false
}) => {
  const [isOpen, setIsOpen] = useState(true);

  const handleHeaderClick = (e: React.MouseEvent) => {
    // Don't toggle collapse if clicking on buttons
    if ((e.target as HTMLElement).closest('button[data-action]')) {
      return;
    }
    setIsOpen(!isOpen);
  };

  return (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl ${(isEditing || isSectionEditing) ? 'ring-2 ring-primary ring-offset-4 shadow-primary/20' : ''}`}>
      <div
        className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50/50 rounded-t-2xl transition-colors duration-200 cursor-pointer"
        onClick={handleHeaderClick}
      >
        <h3 className="text-xl font-bold text-gray-800 flex items-center">
          <div className="w-1 h-6 bg-gradient-to-b from-primary to-primary-dark rounded-full mr-3"></div>
          {title}
          {showEditButton && !isSectionEditing && !isEditing && (
            <button
              data-action="edit"
              onClick={(e) => {
                e.stopPropagation();
                onEdit?.();
              }}
              className="ml-3 p-1.5 text-gray-500 hover:text-primary hover:bg-primary/10 rounded-md transition-all duration-200"
              title="Edit this section"
            >
              <Edit className="w-5 h-5 text-emerald-500" />
            </button>
          )}
        </h3>
        <div className="flex items-center gap-2">
          {(isEditing || isSectionEditing) && (
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
          )}

          {isSectionEditing && (
            <div className="flex items-center gap-1">
              <button
                data-action="save"
                onClick={(e) => {
                  e.stopPropagation();
                  onSave?.();
                }}
                className="p-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-all duration-200"
                title="Save changes"
              >
                <Save className="w-5 h-5" />
              </button>
              <button
                data-action="cancel"
                onClick={(e) => {
                  e.stopPropagation();
                  onCancel?.();
                }}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-all duration-200"
                title="Cancel editing"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          )}

          <ChevronDown className={`w-6 h-6 text-gray-500 transform transition-all duration-300 ${isOpen ? 'rotate-180 text-primary' : 'hover:text-gray-700'}`} />
        </div>
      </div>
      <div className={`overflow-hidden transition-all duration-500 ease-in-out ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}>
        <div className="p-6 border-t border-gray-100">
          {children}
        </div>
      </div>
    </div>
  );
};

export default CollapsibleSection;
