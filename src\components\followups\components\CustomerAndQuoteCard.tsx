import React from 'react';
import { User, Phone, Mail, Package, Clock } from 'lucide-react';
import { Followup, Quote } from '../types';

interface CustomerAndQuoteCardProps {
  followup: Followup;
  isEditing?: boolean;
  onFollowupFieldChange?: (field: keyof Followup, value: string) => void;
  onQuoteFieldChange?: (field: keyof Quote, value: string | number) => void;
}

const CustomerAndQuoteCard: React.FC<CustomerAndQuoteCardProps> = ({
  followup,
  isEditing,
  onFollowupFieldChange,
  onQuoteFieldChange
}) => (
  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
    <div className="flex items-center mb-6">
      <div className="bg-gradient-to-br from-blue-100 to-indigo-100 p-4 rounded-full mr-4 shadow-inner">
        <User className="w-6 h-6 text-blue-600" />
      </div>
      <div className="flex-1">
        <h2 className="text-xl font-bold text-gray-800">{isEditing ? 'Edit Customer & Quote Details' : followup.customer_name}</h2>
        {!isEditing && <p className="text-sm text-gray-500">Customer & Quote Information</p>}
      </div>
    </div>

    {isEditing && onFollowupFieldChange && onQuoteFieldChange ? (
      <div className="space-y-6">
        {/* Customer Information Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
            <div className="w-1 h-5 bg-blue-500 rounded-full mr-3"></div>
            Customer Information
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
              <input
                type="text"
                value={followup.customer_name}
                onChange={(e) => onFollowupFieldChange('customer_name', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                placeholder="Enter customer name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              <input
                type="text"
                value={followup.customer_phone || ''}
                onChange={(e) => onFollowupFieldChange('customer_phone', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
              <input
                type="email"
                value={followup.customer_email || ''}
                onChange={(e) => onFollowupFieldChange('customer_email', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                placeholder="Enter email address"
              />
            </div>
          </div>
        </div>

        {/* Quote Information Section - Only Package Name and Trip Duration */}
        {followup.quotes && (
          <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
              <div className="w-1 h-5 bg-purple-500 rounded-full mr-3"></div>
              Quote Information
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Package Name</label>
                <input
                  type="text"
                  value={followup.quotes.package_name || ''}
                  onChange={(e) => onQuoteFieldChange('package_name', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                  placeholder="Enter package name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Trip Duration</label>
                <input
                  type="text"
                  value={followup.quotes.trip_duration || ''}
                  onChange={(e) => onQuoteFieldChange('trip_duration', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                  placeholder="e.g., 5 Days 4 Nights"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    ) : (
      <div className="space-y-6">
        {/* Customer Information Display */}
        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
            <div className="w-1 h-5 bg-blue-500 rounded-full mr-3"></div>
            Customer Information
          </h3>
          <div className="space-y-3">
            {followup.customer_phone && (
              <div className="flex items-center p-3 bg-blue-50 rounded-xl border border-blue-100">
                <Phone className="w-5 h-5 mr-3 text-blue-600" />
                <span className="text-gray-700 font-medium">{followup.customer_phone}</span>
              </div>
            )}
            {followup.customer_email && (
              <div className="flex items-center p-3 bg-green-50 rounded-xl border border-green-100">
                <Mail className="w-5 h-5 mr-3 text-green-600" />
                <span className="text-gray-700 font-medium">{followup.customer_email}</span>
              </div>
            )}
          </div>
        </div>

        {/* Quote Information Display - Only Package Name and Trip Duration */}
        {followup.quotes && (
          <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
              <div className="w-1 h-5 bg-purple-500 rounded-full mr-3"></div>
              Quote Information
            </h3>
            <div className="space-y-3">
              {followup.quotes.package_name && (
                <div className="flex items-start p-3 bg-purple-50 rounded-xl border border-purple-100">
                  <Package className="w-5 h-5 mr-3 text-purple-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <span className="block text-sm font-medium text-gray-600">Package</span>
                    <span className="text-gray-800 font-semibold">{followup.quotes.package_name}</span>
                  </div>
                </div>
              )}
              {followup.quotes.trip_duration && (
                <div className="flex items-center p-3 bg-indigo-50 rounded-xl border border-indigo-100">
                  <Clock className="w-5 h-5 mr-3 text-indigo-600" />
                  <div>
                    <span className="block text-sm font-medium text-gray-600">Duration</span>
                    <span className="text-gray-800 font-semibold">{followup.quotes.trip_duration}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    )}
  </div>
);

export default CustomerAndQuoteCard;
