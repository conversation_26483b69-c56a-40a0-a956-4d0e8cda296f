import React from 'react';
import { useParams, useLocation } from 'react-router-dom';
import FollowupsList from '../components/followups/FollowupsList';
import FollowupCustomer from '../components/followups/FollowupCustomer';
import CreateFollowup from '../components/followups/CreateFollowup';
import SelectQuote from '../components/followups/SelectQuote';

// Main Component
const Followups: React.FC = () => {
  const { id } = useParams<{ id: string; action: string }>();
  const location = useLocation();

  if (location.pathname === '/followups/create') {
    return <CreateFollowup />;
  }
  if (location.pathname.includes('/saved-quotes') || location.pathname.includes('/select-quote')) {
    return <SelectQuote />;
  }
  if (id) {
    return <FollowupCustomer id={id} />;
  }

  return <FollowupsList />;
};

export default Followups;
