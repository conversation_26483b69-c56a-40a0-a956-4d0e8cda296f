import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { User, Loader, AlertCircle, Plus, Eye, Trash2, Search, Users, IndianRupee, CheckCircle, Hotel, Calendar, Car, Train, Plane, Bus} from 'lucide-react';
import { getQuoteClient } from '../../lib/supabaseManager';
import { Followup } from './types';
import StatusBadge from './components/StatusBadge';
import { formatTravelDate } from './utils';

const FollowupsList: React.FC = () => {
  const [followups, setFollowups] = useState<Followup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [supabase, setSupabase] = useState<any>(null);

  const [searchTerm, setSearchTerm] = useState(() => sessionStorage.getItem('followups_searchTerm') || '');
  const [sortColumn, setSortColumn] = useState<string>(() => (sessionStorage.getItem('followups_sortColumn') as string) || 'customer_name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(() => (sessionStorage.getItem('followups_sortDirection') as 'asc' | 'desc') || 'asc');
  const [currentPage, setCurrentPage] = useState(() => parseInt(sessionStorage.getItem('followups_currentPage') || '1', 10));
  const [itemsPerPage, setItemsPerPage] = useState(() => parseInt(sessionStorage.getItem('followups_itemsPerPage') || '10', 10));

  useEffect(() => {
    sessionStorage.setItem('followups_searchTerm', searchTerm);
    sessionStorage.setItem('followups_sortColumn', sortColumn);
    sessionStorage.setItem('followups_sortDirection', sortDirection);
    sessionStorage.setItem('followups_currentPage', currentPage.toString());
    sessionStorage.setItem('followups_itemsPerPage', itemsPerPage.toString());
  }, [searchTerm, sortColumn, sortDirection, currentPage, itemsPerPage]);

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleDelete = async (followupId: number) => {
    if (!supabase) return;

    if (window.confirm('Are you sure you want to delete this followup?')) {
      try {
        const { error } = await supabase
          .from('followups')
          .delete()
          .eq('id', followupId);

        if (error) throw error;

        setFollowups(followups.filter(f => f.id !== followupId));
      } catch (err) {
        setError(err instanceof Error ? `Error deleting followup: ${err.message}` : 'An unknown error occurred.');
      }
    }
  };

  useEffect(() => {
    const initSupabase = async () => {
      try {
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
        setError('Could not connect to the database.');
        setIsLoading(false);
      }
    };
    initSupabase();
  }, []);

  useEffect(() => {
    if (!supabase) return;

    const fetchFollowups = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('followups')
          .select(`
            *,
            quotes(*)
          `);

        if (error) throw error;
        setFollowups(data as any[] || []);
      } catch (err) {
        setError(err instanceof Error ? `Error: ${err.message}` : 'An unknown error occurred.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFollowups();
  }, [supabase]);

  const sortedAndFilteredFollowups = [...followups]
    .filter(followup =>
      followup.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (followup.customer_phone && followup.customer_phone.includes(searchTerm)) ||
      (followup.customer_email && followup.customer_email.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      let aValue: any;
      let bValue: any;

      if (sortColumn === 'travel_date') {
        aValue = a.quotes?.travel_date;
        bValue = b.quotes?.travel_date;
      } else {
        aValue = a[sortColumn as keyof Followup];
        bValue = b[sortColumn as keyof Followup];
      }

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

  const totalPages = Math.ceil(sortedAndFilteredFollowups.length / itemsPerPage);
  const paginatedFollowups = sortedAndFilteredFollowups.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const getInsights = () => {
    const totalFollowups = followups.length;
    const completed = followups.filter(f => f.hotel_status.toLowerCase().includes('completed') || f.hotel_status.toLowerCase().includes('paid')).length;
    const pending = followups.filter(f => f.hotel_status.toLowerCase().includes('pending')).length;
    const totalProfit = followups.reduce((acc, f) => acc + (f.profit || 0), 0);

    return {
      totalFollowups,
      completed,
      pending,
      totalProfit,
    };
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">Customer Follow-ups</h1>
          <p className="text-gray-600 mt-1">Track and manage customer follow-ups.</p>
        </div>
        <Link to="/followups/select-quote">
          <button className="w-full sm:w-auto bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark flex items-center justify-center shadow-sm transition-all duration-300 ease-in-out transform hover:scale-105">
            <Plus className="w-5 h-5 mr-2" />
            Create Follow-up
          </button>
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm border border-gray-200 mb-4 sm:mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by customer, phone, or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 sm:pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
            />
          </div>
        </div>
      </div>

      {/* Stats */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-blue-900">{getInsights().totalFollowups}</p>
              <p className="text-sm text-blue-700">Total Follow-ups</p>
            </div>
          </div>
          <div className="bg-green-50 p-3 sm:p-4 rounded-lg flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-green-900">{getInsights().completed}</p>
              <p className="text-sm text-green-700">Completed</p>
            </div>
          </div>
          <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Loader className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-yellow-900">{getInsights().pending}</p>
              <p className="text-sm text-yellow-700">Pending</p>
            </div>
          </div>
          <div className="bg-indigo-50 p-3 sm:p-4 rounded-lg flex items-center">
            <div className="bg-indigo-100 p-3 rounded-full">
              <IndianRupee className="w-6 h-6 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-indigo-900">₹{getInsights().totalProfit.toLocaleString()}</p>
              <p className="text-sm text-indigo-700">Total Profit</p>
            </div>
          </div>
        </div>
      </div>

      {/* Table for larger screens */}
      <div className="mt-6 hidden md:block bg-white rounded-lg shadow-md overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('customer_name')}>
                <div className="flex items-center ">
                  <User className="w-4 h-4 mr-2" />Customer {sortColumn === 'customer_name' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('travel_date')}>
                <div className="flex items-center justify-center">
                <Calendar className="w-4 h-4 mr-2" />Travel Date {sortColumn === 'travel_date' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('hotel_status')}>
                <div className="flex items-center justify-center">
                <Hotel className="w-4 h-4 mr-2" /> Hotel {sortColumn === 'hotel_status' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('cab_status')}>
                <div className="flex items-center justify-center">
                <Car className="w-4 h-4 mr-2" />Cab {sortColumn === 'cab_status' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('flight_train_status')}>
                <div className="flex items-center justify-center">
                <Train className="w-4 h-4 mr-1" /> Train/<Plane className="w-4 h-4 mr-1"/>Flight {sortColumn === 'flight_train_status' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('transportation_status')}>
                <div className="flex items-center justify-center">
                <Bus className="w-4 h-4 mr-2" />Transport {sortColumn === 'transportation_status' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('profit')}>
                <div className="flex items-center justify-center">
                <IndianRupee className="w-4 h-4 mr-2" />Profit {sortColumn === 'profit' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr><td colSpan={8} className="text-center py-16"><Loader className="w-10 h-10 text-primary animate-spin inline-block" /><p className="mt-4 text-lg text-gray-600">Loading...</p></td></tr>
            ) : error ? (
              <tr><td colSpan={8} className="text-center py-16"><AlertCircle className="w-10 h-10 text-red-500 inline-block" /><p className="mt-4 text-lg text-red-600">{error}</p></td></tr>
            ) : paginatedFollowups.length === 0 ? (
              <tr><td colSpan={8} className="text-center py-16"><p className="text-lg text-gray-500">{searchTerm ? 'No results.' : 'No follow-ups.'}</p></td></tr>
            ) : (
              paginatedFollowups.map((followup, index) => (
                <tr key={followup.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50 hover:bg-gray-100'}>
                  <td className="px-6 py-4 whitespace-nowrap"><div className="font-medium text-gray-900">{followup.customer_name || 'N/A'}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">{formatTravelDate(followup.quotes?.travel_date)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.hotel_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.cab_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.flight_train_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.transportation_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-green-600">
                    ₹{followup.profit?.toLocaleString() || '0'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Link to={`/followups/${followup.id}`} className="text-primary hover:text-primary-dark p-2 rounded-md hover:bg-gray-200"><Eye className="w-5 h-5" /></Link>
                      <button onClick={() => handleDelete(followup.id)} className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-100"><Trash2 className="w-5 h-5" /></button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Table for mobile screens */}
      <div className="mt-6 md:hidden bg-white rounded-lg shadow-md overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                <div className="flex items-center justify-center">
                  <User className="w-4 h-4 mr-2" />Customer
                </div>
              </th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('travel_date')}>
                Travel Date {sortColumn === 'travel_date' && (sortDirection === 'asc' ? '▲' : '▼')}
              </th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Hotel</th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Cab</th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Flight/Train</th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Transport</th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Profit</th>
              <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr><td colSpan={8} className="text-center py-16"><Loader className="w-10 h-10 text-primary animate-spin inline-block" /><p className="mt-4 text-lg text-gray-600">Loading...</p></td></tr>
            ) : error ? (
              <tr><td colSpan={8} className="text-center py-16"><AlertCircle className="w-10 h-10 text-red-500 inline-block" /><p className="mt-4 text-lg text-red-600">{error}</p></td></tr>
            ) : paginatedFollowups.length === 0 ? (
              <tr><td colSpan={8} className="text-center py-16"><p className="text-lg text-gray-500">{searchTerm ? 'No results.' : 'No follow-ups.'}</p></td></tr>
            ) : (
              paginatedFollowups.map((followup, index) => (
                <tr key={followup.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-4 py-4 whitespace-nowrap text-center"><div className="font-medium text-gray-900 text-sm">{followup.customer_name || 'N/A'}</div></td>
                  <td className="px-4 py-4 whitespace-nowrap text-center text-sm text-gray-900">{formatTravelDate(followup.quotes?.travel_date)}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.hotel_status || 'pending'} /></td>
                  <td className="px-4 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.cab_status || 'pending'} /></td>
                  <td className="px-4 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.flight_train_status || 'pending'} /></td>
                  <td className="px-4 py-4 whitespace-nowrap text-center"><StatusBadge status={followup.transportation_status || 'pending'} /></td>
                  <td className="px-4 py-4 whitespace-nowrap text-center text-sm font-medium text-green-600">
                    ₹{followup.profit?.toLocaleString() || '0'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Link to={`/followups/${followup.id}`} className="text-primary hover:text-primary-dark p-2 rounded-md hover:bg-gray-200"><Eye className="w-5 h-5" /></Link>
                      <button onClick={() => handleDelete(followup.id)} className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-100"><Trash2 className="w-5 h-5" /></button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
          <div className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center justify-center">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="ml-2 px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
            >
              Next
            </button>
            <select
              value={itemsPerPage}
              onChange={e => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="ml-4 px-2 py-1 border border-gray-300 rounded-md"
            >
              <option value={10}>10/page</option>
              <option value={20}>20/page</option>
              <option value={50}>50/page</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

export default FollowupsList;
