import React from 'react';
import { IndianRupee, CheckCircle, Clock } from 'lucide-react';
import { Followup } from '../types';
import { recalculateSubtotal, calculateProfitWithGST, calculateFollowupTotalCost } from '../utils';

interface PaymentSummaryCardProps {
  followup: Followup;
}

const PaymentSummaryCard: React.FC<PaymentSummaryCardProps> = ({ followup }) => {
  const totalCost = calculateFollowupTotalCost(followup);
  const hotelQuoteAmount = followup.hotel_full_amount || 0;
  const hotelAdvanceAmount = followup.hotel_advance_amount || 0;
  const hotelFullAmountPaid = followup.hotel_full_amount_paid || 0;

  const cabQuoteAmount = followup.cab_full_amount || 0;
  const cabAdvanceAmount = followup.cab_advance_amount || 0;
  const cabFullAmountPaid = followup.cab_full_amount_paid || 0;

  const flightTrainPaid = followup.flight_train_full_amount_paid || 0;
  const transportPaid = followup.transportation_full_amount_paid || 0;

  const flightTrainQuoteAmount = followup.flight_train_quote_amount || 0;
  const transportQuoteAmount = followup.transportation_quote_amount || 0;

  // Calculate hotel amounts
  const hotelRemaining = hotelQuoteAmount - hotelAdvanceAmount;
  const hotelProfit = hotelFullAmountPaid > 0 ? hotelQuoteAmount - hotelFullAmountPaid : 0;

  // Calculate cab amounts
  const cabRemaining = cabQuoteAmount - cabAdvanceAmount;
  const cabProfit = cabFullAmountPaid > 0 ? cabQuoteAmount - cabFullAmountPaid : 0;

  // Parse hotel details safely
  const parseHotelDetailsForCalculation = (data: Followup) => {
    if (!data.hotel_details) return [];
    try {
      const parsed = JSON.parse(data.hotel_details);
      if (Array.isArray(parsed)) {
        return parsed.map(item => ({
          name: item.name || 'Unnamed Hotel',
          price: item.price || 0,
          advance: item.advance || 0,
          full_amount_paid: item.full_amount_paid || 0,
          status: item.status || 'pending',
        }));
      }
    } catch (e) {
      // Return empty array if parsing fails
      return [];
    }
    return [];
  };

  // Use existing calculation function for overall profit, GST, and commission
  const hotelDetails = parseHotelDetailsForCalculation(followup);
  // Use subtotal from costs table if available, otherwise calculate
  const costsSubtotal = followup.quotes?.costs?.subtotal;
  const calculatedSubtotal = followup.quotes ? recalculateSubtotal(followup, hotelDetails) : 0;
  const subtotal = costsSubtotal || calculatedSubtotal;
  const calculationResult = followup.quotes ? calculateProfitWithGST(followup, hotelDetails, subtotal) : {
    profit: 0, profitBeforeGst: 0, gst: 0, commission: 0, totalExpenses: 0,
    breakdown: { hotelExpenses: 0, cabExpenses: 0, transportationCost: 0, cabSightseeingCost: 0,trainCost: 0,
                flightCost: 0, ferryCost: 0, marketingCost: 0
    }
  };

  // Add GST amount and commission amount to totalPaid
  const totalPaid = Math.round(hotelFullAmountPaid + hotelAdvanceAmount + cabFullAmountPaid + cabAdvanceAmount + flightTrainPaid + transportPaid + calculationResult.gst + calculationResult.commission);
  const remainingAmount = totalCost - totalPaid;
  const paymentProgress = totalCost > 0 ? (totalPaid / totalCost) * 100 : 0;

  // Enhanced payment completion check with exact status matching
  const isHotelPaid = followup.hotel_status?.includes('full amount paid');
  const isCabPaid = followup.cab_status?.includes('full amount paid');
  const isFlightTrainPaid = (followup.flight_train_full_amount_paid || 0) > 0;
  const isTransportationBooked = (followup.transportation_full_amount_paid || 0) > 0;

  // Check if flight/train is not applicable
  const isFlightTrainNA = followup.flight_train_status === 'no flight/train';
  // Check if transportation is not applicable
  const isTransportationNA = followup.transportation_status === 'no ferry/bus';

  // Check if all applicable payments are completed
  const isAllPaid = isHotelPaid && isCabPaid && (isFlightTrainPaid || isFlightTrainNA) && (isTransportationBooked || isTransportationNA);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-center mb-6">
        <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-3 rounded-full mr-4">
          <IndianRupee className="w-6 h-6 text-green-600" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-gray-800">Payment Summary</h2>
          <p className="text-sm text-gray-500">Complete financial overview</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Payment Progress Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
          <div className="text-center mb-3">
            <span className="text-3xl font-bold text-blue-600">₹{totalPaid.toLocaleString()}</span>
            <span className="text-gray-500 text-lg"> / ₹{totalCost.toLocaleString()}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
            <div
              className="bg-gradient-to-r from-blue-400 to-blue-600 h-4 rounded-full transition-all duration-500"
              style={{ width: `${Math.min(paymentProgress, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-sm font-medium">
            <span className="text-blue-600">Paid ({paymentProgress.toFixed(1)}%)</span>
            {!isAllPaid ? (
              <span className="text-orange-600">Remaining: ₹{remainingAmount.toLocaleString()}</span>
            ) : (
              <span className="text-green-600 font-bold flex items-center">
                <CheckCircle className="w-4 h-4 mr-1" />
                Payment Complete
              </span>
            )}
          </div>
        </div>

        {/* Hotel Payment Details */}
        <div className="space-y-3">
          <div className="flex items-center mb-2">
            <div className="w-1 h-6 bg-purple-500 rounded-full mr-3"></div>
            <h4 className="font-semibold text-gray-700">Hotel Payments</h4>
            {isHotelPaid && <CheckCircle className="w-5 h-5 text-green-500 ml-2" />}
          </div>
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-100 space-y-2 text-sm">
            {hotelQuoteAmount > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Quote Amount:</span>
                <span className="text-gray-800 font-medium">₹{hotelQuoteAmount.toLocaleString()}</span>
              </div>
            )}
            {hotelAdvanceAmount > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Advance Paid:</span>
                <span className="text-green-600 font-medium">₹{hotelAdvanceAmount.toLocaleString()}</span>
              </div>
            )}
            {hotelAdvanceAmount > 0 && hotelFullAmountPaid === 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Remaining:</span>
                <span className="text-red-600 font-medium">₹{hotelRemaining.toLocaleString()}</span>
              </div>
            )}
            {hotelFullAmountPaid > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Full Amount Paid:</span>
                <span className="text-blue-600 font-medium">₹{hotelFullAmountPaid.toLocaleString()}</span>
              </div>
            )}
            {hotelProfit > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Hotel Profit:</span>
                <span className="text-green-600 font-medium">₹{hotelProfit.toLocaleString()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Cab Payment Details */}
        <div className="space-y-3">
          <div className="flex items-center mb-2">
            <div className="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h4 className="font-semibold text-gray-700">Cab Payments</h4>
            {isCabPaid && <CheckCircle className="w-5 h-5 text-green-500 ml-2" />}
          </div>
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-100 space-y-2 text-sm">
            {cabQuoteAmount > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Quote Amount:</span>
                <span className="text-gray-800 font-medium">₹{cabQuoteAmount.toLocaleString()}</span>
              </div>
            )}
            {cabAdvanceAmount > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Advance Paid:</span>
                <span className="text-green-600 font-medium">₹{cabAdvanceAmount.toLocaleString()}</span>
              </div>
            )}
            {cabAdvanceAmount > 0 && cabFullAmountPaid === 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Remaining:</span>
                <span className="text-red-600 font-medium">₹{cabRemaining.toLocaleString()}</span>
              </div>
            )}
            {cabFullAmountPaid > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Full Amount Paid:</span>
                <span className="text-blue-600 font-medium">₹{cabFullAmountPaid.toLocaleString()}</span>
              </div>
            )}
            {cabProfit > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Cab Profit:</span>
                <span className="text-blue-600 font-medium">₹{cabProfit.toLocaleString()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Other Payments */}
        {(flightTrainQuoteAmount > 0 || transportQuoteAmount > 0 || flightTrainPaid > 0 || transportPaid > 0) && (
          <div className="space-y-3">
            <div className="flex items-center mb-2">
              <div className="w-1 h-6 bg-indigo-500 rounded-full mr-3"></div>
              <h4 className="font-semibold text-gray-700">Other Payments</h4>
              {(isFlightTrainPaid && isTransportationBooked) && <CheckCircle className="w-5 h-5 text-green-500 ml-2" />}
            </div>
            <div className="bg-indigo-50 p-3 rounded-lg border border-indigo-100 space-y-2 text-sm">
              {flightTrainQuoteAmount > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Flight/Train Quote:</span>
                  <span className="text-gray-800 font-medium">₹{flightTrainQuoteAmount.toLocaleString()}</span>
                </div>
              )}
              {flightTrainPaid > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Flight/Train Paid:</span>
                  <span className="text-green-600 font-medium">₹{flightTrainPaid.toLocaleString()}</span>
                </div>
              )}
              {transportQuoteAmount > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Transportation Quote:</span>
                  <span className="text-gray-800 font-medium">₹{transportQuoteAmount.toLocaleString()}</span>
                </div>
              )}
              {transportPaid > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Transportation Paid:</span>
                  <span className="text-green-600 font-medium">₹{transportPaid.toLocaleString()}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Financial Summary */}
        <div className="bg-gradient-to-r from-emerald-50 to-green-50 p-4 rounded-xl border border-emerald-200">
          <div className="flex items-center mb-3">
            <div className="w-1 h-6 bg-emerald-500 rounded-full mr-3"></div>
            <h4 className="font-semibold text-gray-700">Financial Summary</h4>
          </div>
          <div className="space-y-2 text-sm">
            {calculationResult.commission > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Commission Amount:</span>
                <span className="text-purple-600 font-medium">₹{calculationResult.commission.toLocaleString()}</span>
              </div>
            )}
            {calculationResult.gst > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">GST Amount (5%):</span>
                <span className="text-orange-600 font-medium">₹{Math.max(0, calculationResult.gst).toLocaleString()}</span>
              </div>
            )}
            {(followup.marketing_amount || 0) > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Marketing Cost:</span>
                <span className="text-gray-600 font-medium">₹{(followup.marketing_amount || 0).toLocaleString()}</span>
              </div>
            )}
            {(followup.add_on_amount || 0) > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Add on Activity:</span>
                <span className="text-blue-600 font-medium">₹{(followup.add_on_amount || 0).toLocaleString()}</span>
              </div>
            )}

            {/* Enhanced Total Profit Section */}
            <div className="mt-4 pt-3 border-t border-emerald-200">
              {isAllPaid ? (
                <div className="bg-green-100 p-3 rounded-lg border-2 border-green-300">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                      <span className="text-green-800 font-semibold">Total Profit</span>
                    </div>
                    <span className={`text-2xl font-bold ${calculationResult.profit >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                      ₹{calculationResult.profit.toLocaleString()}
                    </span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">All payments completed successfully</p>
                </div>
              ) : (
                <div className="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-300">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 text-yellow-600 mr-2" />
                      <span className="text-yellow-800 font-semibold">Expected Profit</span>
                    </div>
                    <span className={`text-xl font-bold ${calculationResult.profit >= 0 ? 'text-yellow-700' : 'text-red-700'}`}>
                      ₹{calculationResult.profit.toLocaleString()}
                    </span>
                  </div>
                  <p className="text-xs text-yellow-600 mt-1">Pending payment completion</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Payment Status Overview */}
        <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
          <h4 className="font-semibold text-gray-700 mb-3">Payment Status Overview</h4>
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className={`flex items-center p-2 rounded-lg ${isHotelPaid ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'}`}>
              {isHotelPaid ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
              <span>Hotel: {followup.hotel_status || 'Pending'}</span>
            </div>
            <div className={`flex items-center p-2 rounded-lg ${isCabPaid ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'}`}>
              {isCabPaid ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
              <span>Cab: {followup.cab_status || 'Pending'}</span>
            </div>
            <div className={`flex items-center p-2 rounded-lg ${
              isFlightTrainPaid || isFlightTrainNA ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'
            }`}>
              {isFlightTrainPaid || isFlightTrainNA ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
              <span>Flight/Train: {followup.flight_train_status || 'Pending'}</span>
            </div>
            <div className={`flex items-center p-2 rounded-lg ${
              isTransportationBooked || isTransportationNA ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'
            }`}>
              {isTransportationBooked || isTransportationNA ? <CheckCircle className="w-5 h-5 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
              <span>Transport: {followup.transportation_status || 'Pending'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSummaryCard;
