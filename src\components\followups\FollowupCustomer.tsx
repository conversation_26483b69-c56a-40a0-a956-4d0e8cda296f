import React, { useState, useEffect } from 'react';
import { Link} from 'react-router-dom';
import { ArrowLeft, Edit, Save, X, Loader, Plus, XCircle} from 'lucide-react';
import { getQuoteClient } from '../../lib/supabaseManager';
import { Followup, HotelDetail, Quote } from './types';
import { parseHotelDetails, recalculateSubtotal, calculateProfitWithGST, calculateFollowupTotalCost } from './utils';
import CustomerAndQuoteCard from './components/CustomerAndQuoteCard';
import PaymentSummaryCard from './components/PaymentSummaryCard';
import CollapsibleSection from './components/CollapsibleSection';
import StatusItem from './components/StatusItem';

const FollowupCustomer: React.FC<{ id: string }> = ({ id }) => {
  const [followup, setFollowup] = useState<Followup | null>(null);
  const [editedFollowup, setEditedFollowup] = useState<Followup | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [supabase, setSupabase] = useState<any>(null);
  const [editedHotelDetails, setEditedHotelDetails] = useState<HotelDetail[]>([]);
  const [notes, setNotes] = useState('');

  // Individual section editing states
  const [editingSections, setEditingSections] = useState({
    hotel: false,
    cab: false,
    flightTrain: false,
    transportation: false,
    otherCost: false,
    notes: false,
  });

  useEffect(() => {
    const initSupabase = async () => {
      try {
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
        setError('Could not connect to the database.');
        setIsLoading(false);
      }
    };
    initSupabase();
  }, []);

  const fetchFollowupData = async () => {
    if (!id || !supabase) return;
    setIsLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from('followups')
        .select(`*, quotes(*, costs(*))`)
        .eq('id', id)
        .single();

      if (error) throw error;
      const followupData = data as any;

      // Ensure costs object is initialized if it doesn't exist
      if (followupData.quotes && !followupData.quotes.costs) {
        followupData.quotes.costs = {
          id: null,
          quote_id: followupData.quotes.id,
          transportation: 0,
          cab_sightseeing: 0,
          train_cost: 0,
          ferry_cost: 0,
          marketing: followupData.marketing_amount || 0,
          gst: 0,
          gst_amount: 0,
          add_on_activity: followupData.add_on_amount || 0,
          commission: 0,
          commission_amount: 0,
          subtotal: 0,
        };
      }

      setFollowup(followupData);
      setEditedFollowup(JSON.parse(JSON.stringify(followupData)));
      setEditedHotelDetails(parseHotelDetails(followupData));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFollowupData();
  }, [id, supabase]);

  useEffect(() => {
    if (followup) {
      setNotes(followup.notes || '');
    }
  }, [followup]);

  const handleHotelDetailChange = (index: number, field: keyof HotelDetail, value: string | number) => {
    const newDetails = [...editedHotelDetails];
    const hotel = newDetails[index];
    (hotel as any)[field] = value;

    if (field === 'advance' || field === 'full_amount_paid') {
        const advance = field === 'advance' ? Number(value) : hotel.advance;
        const fullAmountPaid = field === 'full_amount_paid' ? Number(value) : hotel.full_amount_paid;

        if (fullAmountPaid > 0) {
            hotel.status = 'full amount paid';
        } else if (advance > 0) {
            hotel.status = 'advance paid';
        } else {
            hotel.status = 'pending';
        }
    }
    setEditedHotelDetails(newDetails);
  };

  const addHotel = () => {
    setEditedHotelDetails([...editedHotelDetails, { name: '', price: 0, advance: 0, full_amount_paid: 0, status: 'pending' }]);
  };

  const removeHotel = (index: number) => {
    const newDetails = [...editedHotelDetails];
    newDetails.splice(index, 1);
    setEditedHotelDetails(newDetails);
  };

  const handleFollowupFieldChange = (field: keyof Followup, value: string) => {
    if (editedFollowup) {
      setEditedFollowup({ ...editedFollowup, [field]: value });
    }
  };

  const handleQuoteFieldChange = (field: keyof Quote, value: string | number) => {
    if (editedFollowup && editedFollowup.quotes) {
      setEditedFollowup({
        ...editedFollowup,
        quotes: { ...editedFollowup.quotes, [field]: value },
      });
    }
  };

  // Individual section editing handlers
  const handleSectionEdit = (section: keyof typeof editingSections) => {
    setEditingSections(prev => ({ ...prev, [section]: true }));
  };

  const handleSectionSave = async (section: keyof typeof editingSections) => {
    if (editedFollowup && supabase) {
      try {
        let updatePayload: Partial<Followup> = {};

        // Handle section-specific updates
        switch (section) {
          case 'hotel':
            const hotel_full_amount = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.price || 0), 0);
            const hotel_advance_amount = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.advance || 0), 0);
            const hotel_full_amount_paid = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.full_amount_paid || 0), 0);

            updatePayload = {
              hotel_details: JSON.stringify(editedHotelDetails),
              hotel_full_amount,
              hotel_advance_amount,
              hotel_full_amount_paid,
            };

            if (editedHotelDetails.length > 0) {
              const statuses = editedHotelDetails.map(h => h.status);
              if (statuses.every(s => s === 'full amount paid')) {
                updatePayload.hotel_status = 'full amount paid';
              } else if (statuses.some(s => s === 'pending')) {
                updatePayload.hotel_status = 'pending';
              } else if (statuses.some(s => s === 'advance paid' || s === 'full amount paid')) {
                updatePayload.hotel_status = 'advance paid';
              }
            }
            break;

          case 'cab':
            updatePayload = {
              cab_full_amount: editedFollowup.cab_full_amount,
              cab_advance_amount: editedFollowup.cab_advance_amount,
              cab_full_amount_paid: editedFollowup.cab_full_amount_paid,
              cab_status: editedFollowup.cab_status,
            };
            break;

          case 'flightTrain':
            updatePayload = {
              flight_train_status: editedFollowup.flight_train_status,
              flight_train_quote_amount: editedFollowup.flight_train_quote_amount,
              flight_train_full_amount_paid: editedFollowup.flight_train_full_amount_paid,
            };
            break;

          case 'transportation':
            updatePayload = {
              transportation_status: editedFollowup.transportation_status,
              transportation_quote_amount: editedFollowup.transportation_quote_amount,
              transportation_full_amount_paid: editedFollowup.transportation_full_amount_paid,
            };
            break;

          case 'otherCost':
            updatePayload = {
              marketing_amount: editedFollowup.marketing_amount,
              add_on_amount: editedFollowup.add_on_amount,
              gst_amount: editedFollowup.gst_amount,
              commission_amount: editedFollowup.commission_amount,
            };
            break;

          case 'notes':
            updatePayload = { notes };
            break;
        }

        // Recalculate profit for all sections except notes
        if (section !== 'notes' && Object.keys(updatePayload).length > 0) {
          // Create updated followup object for profit calculation
          const updatedFollowupForCalculation = { ...editedFollowup, ...updatePayload };

          // Use subtotal from costs table if available, otherwise calculate
          const costsSubtotal = updatedFollowupForCalculation.quotes?.costs?.subtotal;
          const calculatedSubtotal = recalculateSubtotal(updatedFollowupForCalculation, editedHotelDetails);
          const subtotal = costsSubtotal || calculatedSubtotal;

          const calculationResult = calculateProfitWithGST(updatedFollowupForCalculation, editedHotelDetails, subtotal);
          const totalCost = calculateFollowupTotalCost(updatedFollowupForCalculation);

          const cab_full_amount_paid = updatedFollowupForCalculation.cab_full_amount_paid || 0;
          const cab_advance_amount = updatedFollowupForCalculation.cab_advance_amount || 0;
          const flightTrainPaid = updatedFollowupForCalculation.flight_train_full_amount_paid || 0;
          const transportPaid = updatedFollowupForCalculation.transportation_full_amount_paid || 0;
          const totalPaid = Math.round(
            (updatedFollowupForCalculation.hotel_full_amount_paid || 0) +
            (updatedFollowupForCalculation.hotel_advance_amount || 0) +
            cab_full_amount_paid +
            cab_advance_amount +
            flightTrainPaid +
            transportPaid +
            calculationResult.gst +
            calculationResult.commission
          );

          const profit = totalCost - totalPaid + calculationResult.commission;

          updatePayload.profit = Math.round(profit);
          updatePayload.total_cost = totalCost;
          updatePayload.total_paid = totalPaid;
        }

        if (Object.keys(updatePayload).length > 0) {
          const { error: followupError } = await supabase
            .from('followups')
            .update(updatePayload)
            .eq('id', editedFollowup.id);

          if (followupError) throw followupError;
        }

        await fetchFollowupData();
        setEditingSections(prev => ({ ...prev, [section]: false }));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred.');
      }
    }
  };

  const handleSectionCancel = (section: keyof typeof editingSections) => {
    // Reset the edited data for this section
    if (followup) {
      setEditedFollowup(JSON.parse(JSON.stringify(followup)));
      setEditedHotelDetails(parseHotelDetails(followup));
      setNotes(followup.notes || '');
    }
    setEditingSections(prev => ({ ...prev, [section]: false }));
  };

  const handleSave = async () => {
    if (editedFollowup && supabase) {
      try {
        const hotel_full_amount = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.price || 0), 0);
        const hotel_advance_amount = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.advance || 0), 0);
        const hotel_full_amount_paid = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.full_amount_paid || 0), 0);

        // Use subtotal from costs table if available, otherwise calculate
        const costsSubtotal = editedFollowup.quotes?.costs?.subtotal;
        const calculatedSubtotal = recalculateSubtotal(editedFollowup, editedHotelDetails);
        const subtotal = costsSubtotal || calculatedSubtotal;
        const calculationResult = calculateProfitWithGST(editedFollowup, editedHotelDetails, subtotal);
        const totalCost = calculateFollowupTotalCost(editedFollowup);

        const cab_full_amount_paid = editedFollowup.cab_full_amount_paid || 0;
        const cab_advance_amount = editedFollowup.cab_advance_amount || 0;
        const flightTrainPaid = editedFollowup.flight_train_full_amount_paid || 0;
        const transportPaid = editedFollowup.transportation_full_amount_paid || 0;
        const totalPaid = Math.round(
            hotel_full_amount_paid +
            hotel_advance_amount +
            cab_full_amount_paid +
            cab_advance_amount +
            flightTrainPaid +
            transportPaid +
            calculationResult.gst +
            calculationResult.commission
        );

        const profit = totalCost - totalPaid + calculationResult.commission;

        const { quotes, ...followupToUpdate } = editedFollowup;

        const updatePayload: Partial<Followup> = {
            ...followupToUpdate,
            hotel_details: JSON.stringify(editedHotelDetails),
            hotel_full_amount,
            hotel_advance_amount,
            hotel_full_amount_paid,
            notes,
            profit,
            marketing_amount: editedFollowup.marketing_amount,
            add_on_amount: editedFollowup.add_on_amount,
            gst_amount: editedFollowup.gst_amount,
            commission_amount: editedFollowup.commission_amount,
            total_cost: totalCost,
            total_paid: totalPaid,
        };

        if (editedHotelDetails.length > 0) {
            const statuses = editedHotelDetails.map(h => h.status);
            if (statuses.every(s => s === 'full amount paid')) {
                updatePayload.hotel_status = 'full amount paid';
            } else if (statuses.some(s => s === 'pending')) {
                updatePayload.hotel_status = 'pending';
            } else if (statuses.some(s => s === 'advance paid' || s === 'full amount paid')) {
                updatePayload.hotel_status = 'advance paid';
            }
        }

        const { error: followupError } = await supabase
          .from('followups')
          .update(updatePayload)
          .eq('id', editedFollowup.id);

        if (followupError) throw followupError;

        if (quotes) {
          const { costs, ...quoteToUpdate } = quotes;
          const updatedQuote = { ...quoteToUpdate, subtotal: subtotal, total_cost: totalCost };
          const { error: quoteError } = await supabase
            .from('quotes')
            .update(updatedQuote)
            .eq('id', quotes.id);
          if (quoteError) throw quoteError;
        }

        await fetchFollowupData();
        setIsEditing(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred.');
      }
    }
  };

  if (isLoading) return <div className="flex justify-center items-center min-h-screen"><Loader className="w-12 h-12 text-primary animate-spin" /></div>;
  if (error) return <div className="p-6 text-center text-red-500">Error: {error}</div>;
  if (!followup) return <div className="p-6 text-center text-gray-500">Follow-up not found</div>;

  const currentFollowup = isEditing && editedFollowup ? editedFollowup : followup;

  if (!currentFollowup) return <div className="flex justify-center items-center min-h-screen"><Loader className="w-12 h-12 text-primary animate-spin" /></div>;

  const hotelDetailsToDisplay = isEditing ? editedHotelDetails : parseHotelDetails(currentFollowup);


  return (
    <div className="p-4 sm:p-6 md:p-8 bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Navigation */}
        <div className="mb-6 sm:mb-8">
          <Link to="/followups" className="inline-flex items-center text-primary hover:text-primary-dark font-semibold transition-colors group">
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Follow-ups
          </Link>
        </div>

        {/* Header Section */}
        <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">Follow-up Details</h1>
              <p className="text-gray-600">Manage customer payments and track progress</p>
            </div>
            <div>
              {isEditing ? (
                <div className="flex gap-3">
                  <button
                    onClick={handleSave}
                    className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-xl hover:from-green-600 hover:to-emerald-700 flex items-center shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                  >
                    <Save className="w-5 h-5 mr-2" />
                    Save Changes
                  </button>
                  <button
                    onClick={() => { setIsEditing(false); fetchFollowupData(); }}
                    className="bg-gray-100 text-gray-700 px-4 sm:px-6 py-2 sm:py-3 rounded-xl hover:bg-gray-200 flex items-center shadow-md transition-all duration-300"
                  >
                    <X className="w-5 h-5 mr-2" />
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-gradient-to-r from-primary to-primary-dark text-white px-4 sm:px-6 py-2 sm:py-3 rounded-xl hover:from-primary-dark hover:to-primary flex items-center shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                >
                  <Edit className="w-5 h-5 mr-2" />
                  Edit Details
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Left Column - Payment Details */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            <CollapsibleSection
              title="Hotel Details"
              isEditing={isEditing}
              isSectionEditing={editingSections.hotel}
              onEdit={() => handleSectionEdit('hotel')}
              onSave={() => handleSectionSave('hotel')}
              onCancel={() => handleSectionCancel('hotel')}
            >
              {(isEditing || editingSections.hotel) ? (
                <div className="space-y-3 sm:space-y-4 max-h-[60vh] overflow-y-auto">
                  {editedHotelDetails.map((hotel, index) => (
                    <div key={index} className="p-3 sm:p-4 border rounded-lg bg-gray-50/50 space-y-3 relative">
                      <button type="button" onClick={() => removeHotel(index)} className="absolute top-2 right-2 text-red-500 hover:text-red-700"><XCircle size={18} /></button>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Hotel Name</label>
                          <input type="text" placeholder="Hotel Name" value={hotel.name} onChange={(e) => handleHotelDetailChange(index, 'name', e.target.value)} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
                          <div>
                              <label className="text-sm font-medium text-gray-700">Quote Amount</label>
                              <input type="number" placeholder="Full Amount" value={hotel.price || ''} onChange={(e) => handleHotelDetailChange(index, 'price', parseFloat(e.target.value) || 0)} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                          </div>
                          <div>
                              <label className="text-sm font-medium text-gray-700">Advance Paid</label>
                              <input type="number" placeholder="Advance Paid" value={hotel.advance || ''} onChange={(e) => handleHotelDetailChange(index, 'advance', parseFloat(e.target.value) || 0)} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                          </div>
                          <div>
                              <label className="text-sm font-medium text-gray-700">Full Amount Paid</label>
                              <input type="number" placeholder="Full Amount Paid" value={hotel.full_amount_paid || ''} onChange={(e) => handleHotelDetailChange(index, 'full_amount_paid', parseFloat(e.target.value) || 0)} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                          </div>
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Remaining</label>
                          <input type="number" placeholder="Remaining" value={(hotel.price || 0) - (hotel.advance || 0)} readOnly className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg bg-gray-100" />
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Status</label>
                          <select value={hotel.status} onChange={(e) => handleHotelDetailChange(index, 'status', e.target.value)} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
                            <option value="pending">Pending</option>
                            <option value="advance paid">Advance Paid</option>
                            <option value="full amount paid">Full Amount Paid</option>
                            <option value="not completed">Not Completed</option>
                          </select>
                      </div>
                    </div>
                  ))}
                  <button type="button" onClick={addHotel} className="text-primary hover:text-primary-dark font-semibold flex items-center gap-2"><Plus size={16} /> Add Hotel</button>
                </div>
              ) : (
                <div className="space-y-3 sm:space-y-4">
                  {hotelDetailsToDisplay.length > 0 ? hotelDetailsToDisplay.map((hotel, index) => (
                    <div key={index} className="p-3 sm:p-4 border rounded-lg bg-gray-50/50 shadow-sm">
                      <div className="flex justify-between items-start">
                        <p className="font-semibold text-md text-gray-800">{hotel.name}</p>
                        <StatusItem
                          label=""
                          value={hotel.status}
                          isEditing={false}
                          options={[]}
                          onChange={() => {}}
                        />
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 text-sm mt-3 pt-3 border-t">
                        <div>
                          <span className="text-gray-500">Quote Amount:</span><br/><span className="font-medium text-gray-800">₹{hotel.price.toLocaleString()}</span>
                        </div>
                        {hotel.advance > 0 && (
                          <>
                            <div>
                              <span className="text-gray-500">Advance Paid:</span><br/><span className="font-medium text-green-600">₹{hotel.advance.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Remaining:</span><br/><span className="font-medium text-red-600">₹{(hotel.price - hotel.advance).toLocaleString()}</span>
                            </div>
                          </>
                        )}
                        {hotel.full_amount_paid > 0 && (
                          <div>
                            <span className="text-gray-500">Full Amount Paid:</span><br/><span className="font-medium text-green-600">₹{hotel.full_amount_paid.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )) : <p className="text-gray-500">No hotel details available.</p>}
                </div>
              )}
            </CollapsibleSection>
            <CollapsibleSection
              title="Cab Details"
              isEditing={isEditing}
              isSectionEditing={editingSections.cab}
              onEdit={() => handleSectionEdit('cab')}
              onSave={() => handleSectionSave('cab')}
              onCancel={() => handleSectionCancel('cab')}
            >
              {(isEditing || editingSections.cab) && editedFollowup ? (
                <div className="space-y-3">

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
                      <div>
                          <label className="text-sm font-medium text-gray-700">Quote Amount</label>
                          <input type="number" placeholder="Full Amount" value={editedFollowup.cab_full_amount || ''} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, cab_full_amount: parseFloat(e.target.value) || 0 })} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Advance Paid</label>
                          <input type="number" placeholder="Advance Paid" value={editedFollowup.cab_advance_amount || ''} onChange={(e) => {
                            if (editedFollowup) {
                                const amount = parseFloat(e.target.value) || 0;
                                setEditedFollowup(prev => {
                                    if (!prev) return null;
                                    let newStatus = prev.cab_status;
                                    if (amount > 0 && newStatus === 'pending') {
                                        newStatus = 'advance paid';
                                    } else if (amount <= 0 && newStatus === 'advance paid') {
                                        newStatus = 'pending';
                                    }
                                    return { ...prev, cab_advance_amount: amount, cab_status: newStatus };
                                });
                            }
                          }} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Full Amount Paid</label>
                          <input type="number" placeholder="Full Amount Paid" value={editedFollowup.cab_full_amount_paid || ''} onChange={(e) => {
                            if (editedFollowup) {
                                const amount = parseFloat(e.target.value) || 0;
                                setEditedFollowup(prev => {
                                    if (!prev) return null;
                                    let newStatus = prev.cab_status;
                                    if (amount > 0) {
                                        newStatus = 'full amount paid';
                                    } else if ((prev.cab_advance_amount || 0) > 0) {
                                        newStatus = 'advance paid';
                                    } else {
                                        newStatus = 'pending';
                                    }
                                    return { ...prev, cab_full_amount_paid: amount, cab_status: newStatus };
                                });
                            }
                          }} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                  </div>
                  <div>
                      <label className="text-sm font-medium text-gray-700">Remaining</label>
                      <input type="number" placeholder="Remaining" value={(editedFollowup.cab_full_amount || 0) - (editedFollowup.cab_advance_amount || 0)} readOnly className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg bg-gray-100" />
                  </div>
                  <div>
                      <label className="text-sm font-medium text-gray-700">Cab Status</label>
                      <select value={editedFollowup.cab_status} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, cab_status: e.target.value })} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
                        <option value="pending">Pending</option>
                        <option value="advance paid">Advance Paid</option>
                        <option value="full amount paid">Full Amount Paid</option>
                        <option value="not completed">Not Completed</option>
                      </select>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-semibold text-md text-gray-800">Cab Service</p>
                      {currentFollowup.cab_name && <p className="text-sm text-gray-500">{currentFollowup.cab_name}</p>}
                    </div>
                    <StatusItem
                      label=""
                      value={currentFollowup.cab_status}
                      isEditing={false}
                      options={[]}
                      onChange={() => {}}
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 text-sm mt-3 pt-3 border-t">
                    <div><span className="text-gray-500">Quote Amount:</span><br/><span className="font-medium text-gray-800">₹{currentFollowup.cab_full_amount?.toLocaleString() ?? '0'}</span></div>
                    {currentFollowup.cab_advance_amount != null && currentFollowup.cab_advance_amount > 0 &&
                    <>
                    <div><span className="text-gray-500">Advance Paid:</span><br/><span className="font-medium text-green-600">₹{currentFollowup.cab_advance_amount.toLocaleString()}</span></div>
                    <div><span className="text-gray-500">Remaining:</span><br/><span className="font-medium text-red-600">₹{((currentFollowup.cab_full_amount || 0) - currentFollowup.cab_advance_amount).toLocaleString()}</span></div>
                    </>
                    }
                    {currentFollowup.cab_full_amount_paid != null && currentFollowup.cab_full_amount_paid > 0 &&
                    <div><span className="text-gray-500">Full Amount Paid:</span><br/><span className="font-medium text-green-600">₹{currentFollowup.cab_full_amount_paid.toLocaleString()}</span></div>
                    }
                  </div>
                </div>
              )}
            </CollapsibleSection>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <CollapsibleSection
                title="Flight/Train Status"
                isEditing={isEditing}
                isSectionEditing={editingSections.flightTrain}
                onEdit={() => handleSectionEdit('flightTrain')}
                onSave={() => handleSectionSave('flightTrain')}
                onCancel={() => handleSectionCancel('flightTrain')}
              >
                {(isEditing || editingSections.flightTrain) ? (
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Quote Amount</label>
                        <input type="number" placeholder="Quote Amount" value={editedFollowup?.flight_train_quote_amount || ''} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, flight_train_quote_amount: parseFloat(e.target.value) || 0 })} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Full Amount Paid</label>
                        <input type="number" placeholder="Full Amount Paid" value={editedFollowup?.flight_train_full_amount_paid || ''} onChange={(e) => {
                          if (editedFollowup) {
                            const amount = parseFloat(e.target.value) || 0;
                            setEditedFollowup(prev => {
                              if (!prev) return null;
                              let newStatus = prev.flight_train_status;
                              if (amount > 0) {
                                newStatus = 'flight paid';
                              } else {
                                newStatus = 'pending';
                              }
                              return { ...prev, flight_train_full_amount_paid: amount, flight_train_status: newStatus };
                            });
                          }
                        }} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Remaining</label>
                        <input type="number" placeholder="Remaining" value={(editedFollowup?.flight_train_quote_amount || 0) - (editedFollowup?.flight_train_full_amount_paid || 0)} readOnly className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg bg-gray-100" />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Flight/Train Status</label>
                      <select value={editedFollowup?.flight_train_status} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, flight_train_status: e.target.value })} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
                        <option value="pending">Pending</option>
                        <option value="flight paid">Flight Paid</option>
                        <option value="train paid">Train Paid</option>
                        <option value="flight not paid">Flight Not Paid</option>
                        <option value="train not paid">Train Not Paid</option>
                        <option value="no flight/train">No Flight/Train</option>
                      </select>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-semibold text-md text-gray-800">Flight/Train</p>
                      </div>
                      <StatusItem
                        label=""
                        value={currentFollowup.flight_train_status}
                        isEditing={false}
                        options={[]}
                        onChange={() => {}}
                      />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 text-sm mt-3 pt-3 border-t">
                      <div>
                        <span className="text-gray-500">Quote Amount:</span><br/><span className="font-medium text-gray-800">₹{currentFollowup.flight_train_quote_amount?.toLocaleString() || '0'}</span>
                      </div>
                      {currentFollowup.flight_train_full_amount_paid != null && currentFollowup.flight_train_full_amount_paid > 0 &&
                      <div>
                        <span className="text-gray-500">Full Amount Paid:</span><br/><span className="font-medium text-green-600">₹{currentFollowup.flight_train_full_amount_paid.toLocaleString()}</span>
                      </div>
                      }
                      {currentFollowup.flight_train_full_amount_paid != null && currentFollowup.flight_train_full_amount_paid > 0 && (currentFollowup.flight_train_quote_amount || 0) > currentFollowup.flight_train_full_amount_paid &&
                      <div>
                        <span className="text-gray-500">Remaining:</span><br/><span className="font-medium text-red-600">₹{((currentFollowup.flight_train_quote_amount || 0) - currentFollowup.flight_train_full_amount_paid).toLocaleString()}</span>
                      </div>
                      }
                    </div>
                  </div>
                )}
              </CollapsibleSection>

              <CollapsibleSection
                title="Transportation Status"
                isEditing={isEditing}
                isSectionEditing={editingSections.transportation}
                onEdit={() => handleSectionEdit('transportation')}
                onSave={() => handleSectionSave('transportation')}
                onCancel={() => handleSectionCancel('transportation')}
              >
                {(isEditing || editingSections.transportation) ? (
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Quote Amount</label>
                        <input type="number" placeholder="Quote Amount" value={editedFollowup?.transportation_quote_amount || ''} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, transportation_quote_amount: parseFloat(e.target.value) || 0 })} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Full Amount Paid</label>
                        <input type="number" placeholder="Full Amount Paid" value={editedFollowup?.transportation_full_amount_paid || ''} onChange={(e) => {
                          if (editedFollowup) {
                            const amount = parseFloat(e.target.value) || 0;
                            setEditedFollowup(prev => {
                              if (!prev) return null;
                              let newStatus = prev.transportation_status;
                              if (amount > 0) {
                                newStatus = 'ferry booked';
                              } else {
                                newStatus = 'pending';
                              }
                              return { ...prev, transportation_full_amount_paid: amount, transportation_status: newStatus };
                            });
                          }
                        }} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Remaining</label>
                        <input type="number" placeholder="Remaining" value={(editedFollowup?.transportation_quote_amount || 0) - (editedFollowup?.transportation_full_amount_paid || 0)} readOnly className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg bg-gray-100" />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Transportation Status</label>
                      <select value={editedFollowup?.transportation_status} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, transportation_status: e.target.value })} className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
                        <option value="pending">Pending</option>
                        <option value="ferry booked">Ferry Booked</option>
                        <option value="bus booked">Bus Booked</option>
                        <option value="ferry not booked">Ferry Not Booked</option>
                        <option value="bus not booked">Bus Not Booked</option>
                        <option value="no ferry/bus">No Ferry/Bus</option>
                      </select>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-semibold text-md text-gray-800">Transportation</p>
                      </div>
                      <StatusItem
                        label=""
                        value={currentFollowup.transportation_status}
                        isEditing={false}
                        options={[]}
                        onChange={() => {}}
                      />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 text-sm mt-3 pt-3 border-t">
                      <div>
                        <span className="text-gray-500">Quote Amount:</span><br/><span className="font-medium text-gray-800">₹{currentFollowup.transportation_quote_amount?.toLocaleString() || '0'}</span>
                      </div>
                      {currentFollowup.transportation_full_amount_paid != null && currentFollowup.transportation_full_amount_paid > 0 &&
                      <div>
                        <span className="text-gray-500">Full Amount Paid:</span><br/><span className="font-medium text-green-600">₹{currentFollowup.transportation_full_amount_paid.toLocaleString()}</span>
                      </div>
                      }
                      {currentFollowup.transportation_full_amount_paid != null && currentFollowup.transportation_full_amount_paid > 0 && (currentFollowup.transportation_quote_amount || 0) > currentFollowup.transportation_full_amount_paid &&
                      <div>
                        <span className="text-gray-500">Remaining:</span><br/><span className="font-medium text-red-600">₹{((currentFollowup.transportation_quote_amount || 0) - currentFollowup.transportation_full_amount_paid).toLocaleString()}</span>
                      </div>
                      }
                    </div>
                  </div>
                )}
              </CollapsibleSection>
            </div>
            {/* Split into two columns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <CollapsibleSection
                title="Other Cost"
                isEditing={isEditing}
                isSectionEditing={editingSections.otherCost}
                onEdit={() => handleSectionEdit('otherCost')}
                onSave={() => handleSectionSave('otherCost')}
                onCancel={() => handleSectionCancel('otherCost')}
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {/* Marketing Amount - No Status, Only Amount */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">Marketing Cost</p>
                    {(isEditing || editingSections.otherCost) ? (
                      <input
                        type="number"
                        placeholder='Marketing cost'
                        value={editedFollowup?.marketing_amount || ''}
                        onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, marketing_amount: parseFloat(e.target.value) || 0 })}
                        className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-gray-800">₹{currentFollowup.marketing_amount?.toLocaleString() || '0'}</p>
                      </div>
                    )}
                  </div>

                  {/* Add on Activity Amount - Editable */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">Add on Activity</p>
                    {(isEditing || editingSections.otherCost) ? (
                      <input
                        type="number"
                        placeholder='Add on cost'
                        value={editedFollowup?.add_on_amount || ''}
                        onChange={(e) => {
                          if (editedFollowup) {
                            setEditedFollowup({
                              ...editedFollowup,
                              add_on_amount: parseFloat(e.target.value) || 0
                            });
                          }
                        }}
                        className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-blue-600">₹{(currentFollowup?.add_on_amount || 0).toLocaleString()}</p>
                      </div>
                    )}
                  </div>

                  {/* GST Amount - Editable */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">GST Amount</p>
                    {(isEditing || editingSections.otherCost) ? (
                      <input
                        type="number"
                        placeholder='GST amount'
                        value={editedFollowup?.gst_amount || ''}
                        onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, gst_amount: parseFloat(e.target.value) || 0 })}
                        className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-orange-600">₹{(currentFollowup?.gst_amount || 0).toLocaleString()}</p>
                      </div>
                    )}
                  </div>

                  {/* Commission Amount - Editable */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">Commission Amount</p>
                    {(isEditing || editingSections.otherCost) ? (
                      <input
                        type="number"
                        placeholder='Commission amount'
                        value={editedFollowup?.commission_amount || ''}
                        onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, commission_amount: parseFloat(e.target.value) || 0 })}
                        className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-purple-600">₹{(currentFollowup?.commission_amount || 0).toLocaleString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CollapsibleSection>

              {/* Notes Section - Moved to second column */}
              <CollapsibleSection
                title="Notes"
                isEditing={isEditing}
                isSectionEditing={editingSections.notes}
                onEdit={() => handleSectionEdit('notes')}
                onSave={() => handleSectionSave('notes')}
                onCancel={() => handleSectionCancel('notes')}
              >
                  <textarea
                      placeholder={(isEditing || editingSections.notes) ? "Add a new note..." : "No notes available."}
                      className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary shadow-sm"
                      rows={5}
                      value={(isEditing || editingSections.notes) ? notes : currentFollowup.notes || ''}
                      onChange={(e) => setNotes(e.target.value)}
                      disabled={!(isEditing || editingSections.notes)}
                  ></textarea>
              </CollapsibleSection>
            </div>
          </div>

          {/* Right Column - Customer Info & Payment Summary */}
          <div className="xl:col-span-1 space-y-6 sm:space-y-8">
            <CustomerAndQuoteCard
              followup={currentFollowup}
              isEditing={isEditing}
              onFollowupFieldChange={handleFollowupFieldChange}
              onQuoteFieldChange={handleQuoteFieldChange}
            />
            <PaymentSummaryCard followup={currentFollowup} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FollowupCustomer;
