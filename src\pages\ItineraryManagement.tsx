import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, Search, RotateCcw, FileText, Users, Package, Calendar, MapPin, Clock } from 'lucide-react';
import { getQuoteClient } from '../lib/supabaseManager';
import {
  fetchItineraryTemplates,
  fetchQuoteItineraries,
  fetchDestinations,
  createItineraryTemplate,
  updateItineraryTemplate,
  deleteItineraryTemplate,
  ItineraryTemplate,
  QuoteItinerary
} from '../services/itineraryService';

const NIGHTS_DAYS_OPTIONS = [
  '1N/ 2D', '2N/ 3D', '3N/ 4D', '4N/ 5D', '5N/ 6D', 
  '6N/ 7D', '7N/ 8D', '8N/ 9D', '9N/ 10D', '10N/ 11D', 'Custom'
];

const ItineraryManagement: React.FC = () => {
  const [templates, setTemplates] = useState<ItineraryTemplate[]>([]);
  const [quotes, setQuotes] = useState<QuoteItinerary[]>([]);
  const [destinations, setDestinations] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ItineraryTemplate | null>(null);
  const [selectedQuote, setSelectedQuote] = useState<QuoteItinerary | null>(null);
  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ItineraryTemplate | null>(null);
  const [editingItinerary, setEditingItinerary] = useState(false);
  const [editedItineraryText, setEditedItineraryText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  // TEMPORARILY DISABLED: Quotes itinerary functionality as per requirements
  // const [activeView, setActiveView] = useState<'quotes' | 'templates'>('quotes');
  const activeView = 'templates'; // Fixed to templates only
  const [destinationFilter, setDestinationFilter] = useState('');
  const [nightsFilter, setNightsFilter] = useState('');
  const [templateDestinationFilter, setTemplateDestinationFilter] = useState('');

  // Compute unique destinations from existing templates
  const templateDestinations = React.useMemo(() => {
    return [...new Set(templates.map(template => template.name.split('-')[0]).filter(Boolean))];
  }, [templates]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(9);
  const [totalItems, setTotalItems] = useState(0);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    destination: '',
    customDestination: '',
    nights_days: '',
    customNightsDays: '',
    content: ''
  });

  // Fetch data on component mount
  useEffect(() => {
    loadQuotes();
    loadTemplates();
    loadDestinations();
  }, []);

  const loadQuotes = async () => {
    setIsLoading(true);
    try {
      const data = await fetchQuoteItineraries();
      setQuotes(data);
    } catch (error) {
      console.error('Error fetching quotes:', error);
      alert('Failed to fetch quote itineraries');
    } finally {
      setIsLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const data = await fetchItineraryTemplates();
      setTemplates(data);
    } catch (error) {
      console.error('Error fetching templates:', error);
      alert('Failed to fetch itinerary templates');
    }
  };

  const loadDestinations = async () => {
    try {
      const data = await fetchDestinations();
      setDestinations(data);
    } catch (error) {
      console.error('Error fetching destinations:', error);
      // Error handling is already done in the service
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      destination: '',
      customDestination: '',
      nights_days: '',
      customNightsDays: '',
      content: ''
    });
  };

  const handleAdd = () => {
    resetForm();
    setEditingTemplate(null);
    setShowAddModal(true);
  };

  const handleEdit = (template: ItineraryTemplate) => {
    // Parse the name to extract destination and nights_days
    const nameParts = template.name.split('-');
    const destination = nameParts[0] || '';
    const nightsDays = nameParts[1]?.replace('&', '/') || '';

    setFormData({
      name: template.name,
      destination: destination,
      customDestination: '',
      nights_days: nightsDays,
      customNightsDays: '',
      content: template.content || ''
    });
    setEditingTemplate(template);
    setShowAddModal(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this itinerary template?')) {
      return;
    }

    try {
      await deleteItineraryTemplate(id);
      setTemplates(templates.filter(t => t.id !== id));
      alert('Template deleted successfully!');
    } catch (error) {
      console.error('Error deleting template:', error);
      alert('Failed to delete template');
    }
  };

  const handleEditItinerary = () => {
    if (selectedQuote) {
      setEditedItineraryText(selectedQuote.itinerary || '');
      setEditingItinerary(true);
    }
  };

  const handleSaveEditedItinerary = async () => {
    if (!selectedQuote) return;

    try {
      const supabase = await getQuoteClient();
      const { error } = await supabase
        .from('quotes')
        .update({ itinerary: editedItineraryText })
        .eq('id', selectedQuote.id);

      if (error) throw error;

      // Update local state
      setQuotes(quotes.map(quote =>
        quote.id === selectedQuote.id
          ? { ...quote, itinerary: editedItineraryText }
          : quote
      ));

      setSelectedQuote({ ...selectedQuote, itinerary: editedItineraryText });
      setEditingItinerary(false);
      alert('Itinerary updated successfully!');
    } catch (error) {
      console.error('Error updating itinerary:', error);
      alert('Failed to update itinerary');
    }
  };

  const handleDeleteQuote = async () => {
    if (!selectedQuote) return;

    if (!confirm('Are you sure you want to delete this quote? This action cannot be undone.')) {
      return;
    }

    try {
      const supabase = await getQuoteClient();
      const { error } = await supabase
        .from('quotes')
        .delete()
        .eq('id', selectedQuote.id);

      if (error) throw error;

      // Update local state
      setQuotes(quotes.filter(quote => quote.id !== selectedQuote.id));
      setShowQuoteModal(false);
      setSelectedQuote(null);
      alert('Quote deleted successfully!');
    } catch (error) {
      console.error('Error deleting quote:', error);
      alert('Failed to delete quote');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const finalDestination = formData.destination === 'Custom'
      ? formData.customDestination
      : formData.destination;

    const finalNightsDays = formData.nights_days === 'Custom'
      ? formData.customNightsDays
      : formData.nights_days;

    if (!finalDestination || !finalNightsDays) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      // Format name as "Destination-NightsNDays"
      const formattedName = `${finalDestination}-${finalNightsDays.replace('/', '&')}`;

      const templateData = {
        name: formattedName,
        content: formData.content
      };

      if (editingTemplate) {
        // Update existing template
        await updateItineraryTemplate(editingTemplate.id, templateData);
        alert('Template updated successfully!');
      } else {
        // Create new template
        await createItineraryTemplate(templateData);
        alert('Template created successfully!');
      }

      setShowAddModal(false);
      resetForm();
      loadTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      alert('Failed to save template');
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchTerm ||
      (template.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.content || '').toLowerCase().includes(searchTerm.toLowerCase());

    // Extract destination from template name (format: "Vietnam-5N&6D")
    const templateDestination = template.name ? template.name.split('-')[0] : '';
    const matchesDestination = !templateDestinationFilter || templateDestination === templateDestinationFilter;

    return matchesSearch && matchesDestination;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredTemplates.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);

  // Update total items when filtered templates change
  React.useEffect(() => {
    setTotalItems(filteredTemplates.length);
    // Reset to first page if current page is beyond available pages
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [filteredTemplates.length, currentPage, totalPages]);

  // Pagination helper functions
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = !searchTerm ||
      (quote.package_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (quote.customer_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (quote.destination || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (quote.trip_duration || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDestination = !destinationFilter || quote.destination === destinationFilter;
    const matchesNights = !nightsFilter || quote.trip_duration === nightsFilter;

    return matchesSearch && matchesDestination && matchesNights;
  });

  return (
    <div className="min-h-screen bg-gray-50 border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-1 flex flex-col items-center">
          <div className="text-center mb-1">
            <h1 className="text-4xl font-bold text-[#00B69B] mb-1">Itinerary Management</h1>
          </div>
        </div>

      {/* Header Actions */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-bold text-gray-800">
              {/* TEMPORARILY DISABLED: Show only templates */}
              {/* {activeView === 'quotes' ? 'Quote Itineraries' : 'Saved Templates'} */}
              Saved Templates
            </h2>
            {/* TEMPORARILY DISABLED: Quotes/Templates toggle - showing only templates */}
            {/*
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveView('quotes')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeView === 'quotes'
                    ? 'bg-[#00B69B] text-white'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <Users size={16} className="inline mr-2" />
                Quotes
              </button>
              <button
                onClick={() => setActiveView('templates')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeView === 'templates'
                    ? 'bg-[#00B69B] text-white'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <FileText size={16} className="inline mr-2" />
                Templates
              </button>
            </div>
            */}
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <button
              // TEMPORARILY DISABLED: Only templates refresh
              // onClick={() => activeView === 'quotes' ? loadQuotes() : loadTemplates()}
              onClick={() => loadTemplates()}
              className="px-4 py-2 flex items-center gap-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <RotateCcw size={14} />
              Refresh
            </button>
            {activeView === 'templates' && (
              <button
                onClick={handleAdd}
                className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors flex items-center gap-2"
              >
                <Plus size={16} />
                Add Template
              </button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mt-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="Search by name or content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>

            {/* TEMPORARILY DISABLED: Quotes-specific filters */}
            {/*
            {activeView === 'quotes' && (
              <>
                <div className="sm:w-48">
                  <select
                    value={destinationFilter}
                    onChange={(e) => setDestinationFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent text-sm"
                  >
                    <option value="">All Destinations</option>
                    {destinations.map((dest) => (
                      <option key={dest} value={dest}>{dest}</option>
                    ))}
                  </select>
                </div>

                <div className="sm:w-48">
                  <select
                    value={nightsFilter}
                    onChange={(e) => setNightsFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent text-sm"
                  >
                    <option value="">All Durations</option>
                    {NIGHTS_DAYS_OPTIONS.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                {(destinationFilter || nightsFilter) && (
                  <button
                    onClick={() => {
                      setDestinationFilter('');
                      setNightsFilter('');
                    }}
                    className="px-3 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors text-sm whitespace-nowrap"
                  >
                    Clear Filters
                  </button>
                )}
              </>
            )}
            */}

            {activeView === 'templates' && (
              <>
                <div className="sm:w-48">
                  <select
                    value={templateDestinationFilter}
                    onChange={(e) => setTemplateDestinationFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent text-sm"
                  >
                    <option value="">All Destinations</option>
                    {templateDestinations.map((dest) => (
                      <option key={dest} value={dest}>{dest}</option>
                    ))}
                  </select>
                </div>

                {templateDestinationFilter && (
                  <button
                    onClick={() => setTemplateDestinationFilter('')}
                    className="px-3 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors text-sm whitespace-nowrap"
                  >
                    Clear Filter
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Content Grid */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
        {isLoading ? (
          <div className="flex flex-col justify-center items-center py-20">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#00B69B] mb-4"></div>
            <p className="text-gray-600 text-lg font-medium">Loading itineraries...</p>
            <p className="text-gray-500 text-sm mt-1">Please wait while we fetch your data</p>
          </div>
        ) : false ? ( // TEMPORARILY DISABLED: Quotes view
          /* DISABLED QUOTES SECTION - PRESERVED FOR FUTURE USE */
          // Quotes Grid
          filteredQuotes.length === 0 ? (
            <div className="text-center py-20">
              <div className="bg-gray-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Package size={32} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-3">
                {searchTerm ? 'No Matching Itineraries' : 'No Quote Itineraries Yet'}
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                {searchTerm
                  ? 'Try adjusting your search terms or filters to find what you\'re looking for.'
                  : 'Quotes with detailed itineraries will appear here. Create quotes with itinerary content to get started.'
                }
              </p>
              {searchTerm && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setDestinationFilter('');
                    setNightsFilter('');
                  }}
                  className="px-6 py-3 bg-[#00B69B] text-white rounded-lg hover:bg-[#008577] transition-colors font-medium"
                >
                  Clear Search & Filters
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
              {filteredQuotes.map((quote) => (
                <div
                  key={quote.id}
                  className="bg-white border border-gray-300 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:border-[#00B69B]/20 transition-all duration-300 cursor-pointer group"
                  onClick={() => {
                    setSelectedQuote(quote);
                    setShowQuoteModal(true);
                  }}
                >
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-bold text-gray-900 truncate text-base mb-2 group-hover:text-[#00B69B] transition-colors ">
                        {quote.package_name}
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                        <Users size={14} className="text-gray-400" />
                        <span className="font-medium">{quote.customer_name}</span>

                        <MapPin size={14} className="text-gray-400" />
                        <span className="font-medium">{quote.destination}</span> 

                        <Clock size={14} className="text-gray-400" />
                        <span className="font-medium">{quote.trip_duration}</span>
                      </div>
                    </div>
                  </div>


                  {quote.itinerary && (
                    <div className="bg-gray-50 rounded-lg p-3 mb-4">
                      <div className="text-sm text-gray-700 whitespace-pre-line">
                        {quote.itinerary.split('\n').slice(0, 18).join('\n')}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1 text-xs text-gray-400">
                      <Calendar size={12} />
                      <span>{new Date(quote.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="text-xs text-[#00B69B] font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                      View Details →
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )
        ) : (
          // Templates Grid
          filteredTemplates.length === 0 ? (
            <div className="text-center py-20">
              <div className="bg-gray-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <FileText size={32} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-3">
                {searchTerm ? 'No Matching Templates' : 'No Templates Yet'}
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                {searchTerm
                  ? 'Try adjusting your search terms or filters to find what you\'re looking for.'
                  : 'Create reusable itinerary templates to streamline your workflow and maintain consistency across similar trips.'
                }
              </p>
              {!searchTerm && (
                <button
                  onClick={handleAdd}
                  className="px-6 py-3 bg-[#00B69B] text-white rounded-lg hover:bg-[#008577] transition-colors font-medium flex items-center gap-2 mx-auto"
                >
                  <Plus size={18} />
                  Create Your First Template
                </button>
              )}
              {searchTerm && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setTemplateDestinationFilter('');
                  }}
                  className="px-6 py-3 bg-[#00B69B] text-white rounded-lg hover:bg-[#008577] transition-colors font-medium"
                >
                  Clear Search & Filters
                </button>
              )}
            </div>
          ) : (
            <div>
              {/* Pagination Info and Controls */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-600">
                    Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} templates
                  </div>
                  <div className="flex items-center gap-2">
                    <label className="text-sm text-gray-600">Show:</label>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                      className="px-2 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    >
                      <option value={9}>9</option>
                      <option value={21}>21</option>
                      <option value={50}>50</option>
                    </select>
                    <span className="text-sm text-gray-600">per page</span>
                  </div>
                </div>

                {/* Page Navigation */}
                {totalPages > 1 && (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    {getPageNumbers().map((page) => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-1 border rounded-md text-sm ${
                          currentPage === page
                            ? 'bg-[#00B69B] text-white border-[#00B69B]'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>

              {/* Templates Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
                {paginatedTemplates.map((template) => {
                return (
                  <div
                    key={template.id}
                    className="bg-white border border-gray-300 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:border-[#00B69B]/20 transition-all duration-300 cursor-pointer group"
                    onClick={() => {
                      setSelectedTemplate(template);
                      setShowTemplateModal(true);
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 text-lg mb-2 group-hover:text-[#00B69B] transition-colors truncate">
                          {template.name}</h3>                        
                      </div>
                    </div>

                    {template.content && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <div className="text-sm text-gray-700 whitespace-pre-line">
                          {template.content.split('\n').slice(0,18).join('\n')}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-xs text-gray-400">
                        <Calendar size={12} />
                        <span>{new Date(template.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="text-xs text-[#00B69B] font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                        View Details →
                      </div>
                    </div>
                  </div>
                );
              })}
              </div>

              {/* Bottom Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-2 mt-8 pt-6 border-t border-gray-200">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex items-center gap-1">
                    {getPageNumbers().map((page) => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-2 border rounded-md text-sm ${
                          currentPage === page
                            ? 'bg-[#00B69B] text-white border-[#00B69B]'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )
        )}
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-800">
                  {editingTemplate ? 'Edit Itinerary Template' : 'Add New Itinerary Template'}
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                >
                  <X size={20} />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Destination */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Destination *
                  </label>
                  <select
                    value={formData.destination}
                    onChange={(e) => setFormData({ ...formData, destination: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    required
                  >
                    <option value="">Select Destination</option>
                    {destinations.map((dest) => (
                      <option key={dest} value={dest}>{dest}</option>
                    ))}
                    <option value="Custom">Custom</option>
                  </select>
                </div>

                {/* Custom Destination */}
                {formData.destination === 'Custom' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Custom Destination *
                    </label>
                    <input
                      type="text"
                      value={formData.customDestination}
                      onChange={(e) => setFormData({ ...formData, customDestination: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                      placeholder="Enter custom destination"
                      required
                    />
                  </div>
                )}

                {/* Nights/Days */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nights/Days *
                  </label>
                  <select
                    value={formData.nights_days}
                    onChange={(e) => setFormData({ ...formData, nights_days: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    required
                  >
                    <option value="">Select Duration</option>
                    {NIGHTS_DAYS_OPTIONS.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                {/* Custom Nights/Days */}
                {formData.nights_days === 'Custom' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Custom Duration *
                    </label>
                    <input
                      type="text"
                      value={formData.customNightsDays}
                      onChange={(e) => setFormData({ ...formData, customNightsDays: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                      placeholder="e.g., 12N/ 13D"
                      required
                    />
                  </div>
                )}

                {/* Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Itinerary Content
                  </label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    rows={18}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    placeholder="Enter detailed itinerary content..."
                  />
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors flex items-center gap-2"
                  >
                    <Save size={16} />
                    {editingTemplate ? 'Update Template' : 'Create Template'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Quote Detail Modal */}
      {showQuoteModal && selectedQuote && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{selectedQuote.package_name}</h3>
                  <p className="text-base font-medium text-gray-800 mt-1">
                    {selectedQuote.customer_name} • {selectedQuote.destination} • {selectedQuote.trip_duration}
                  </p>
                </div>
                <button
                  onClick={() => {
                    setShowQuoteModal(false);
                    setSelectedQuote(null);
                  }}
                  className="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Itinerary Content */}
              <div className="mb-6">
                <h4 className="text-lg font-medium text-gray-800 mb-4">Itinerary Details</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  {editingItinerary ? (
                    <div>
                      <textarea
                        value={editedItineraryText}
                        onChange={(e) => setEditedItineraryText(e.target.value)}
                        rows={18}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B] text-sm"
                        placeholder="Enter itinerary content..."
                      />
                      <div className="flex justify-end gap-2 mt-3">
                        <button
                          onClick={() => {
                            setEditingItinerary(false);
                            setEditedItineraryText(selectedQuote.itinerary || '');
                          }}
                          className="px-3 py-1 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors text-sm"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleSaveEditedItinerary}
                          className="px-3 py-1 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors text-sm"
                        >
                          Save Changes
                        </button>
                      </div>
                    </div>
                  ) : (
                    <>
                      {selectedQuote.itinerary ? (
                        <div className="text-sm text-gray-700 whitespace-pre-line">
                          {selectedQuote.itinerary}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">No itinerary content available</p>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowQuoteModal(false);
                    setSelectedQuote(null);
                    setEditingItinerary(false);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={handleEditItinerary}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <Edit size={16} />
                  {editingItinerary ? 'Cancel Edit' : 'Edit Itinerary'}
                </button>
                <button
                  onClick={handleDeleteQuote}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                >
                  <Trash2 size={16} />
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Template Detail Modal */}
      {showTemplateModal && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{selectedTemplate.name}</h3>
                  <p className="text-base font-medium text-gray-800 mt-1">
                    Itinerary Template
                  </p>
                </div>
                <button
                  onClick={() => {
                    setShowTemplateModal(false);
                    setSelectedTemplate(null);
                  }}
                  className="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Template Content */}
              <div className="mb-6">
                <h4 className="text-lg font-medium text-gray-800 mb-4">Template Details</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  {selectedTemplate.content ? (
                    <div className="text-sm text-gray-700 whitespace-pre-line">
                      {selectedTemplate.content}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic">No template content available</p>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowTemplateModal(false);
                    setSelectedTemplate(null);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    handleEdit(selectedTemplate);
                    setShowTemplateModal(false);
                    setSelectedTemplate(null);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <Edit size={16} />
                  Edit Template
                </button>
                <button
                  onClick={() => {
                    handleDelete(selectedTemplate.id);
                    setShowTemplateModal(false);
                    setSelectedTemplate(null);
                  }}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                >
                  <Trash2 size={16} />
                  Delete Template
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default ItineraryManagement;
