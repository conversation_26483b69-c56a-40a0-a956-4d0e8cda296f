export interface Invoice {
  id: string;
  quote_id: string;
  invoice_number: string;
  invoice_date: string;
  due_date: string | null;
  subtotal: number;
  igst_total: number;
  grand_total: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'cancelled';
  created_at: string;
  updated_at: string;
  // Additional fields from quotes table for display
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  destination?: string;
  package_name?: string;
}