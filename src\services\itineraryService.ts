import { getQuoteClient } from '../lib/supabaseManager';

export interface ItineraryTemplate {
  id: string;
  name: string;
  content?: string;
  created_at: string;
}

export interface CreateItineraryTemplate {
  name: string;
  content?: string;
}

export interface UpdateItineraryTemplate {
  name?: string;
  content?: string;
}

export interface QuoteItinerary {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  trip_duration: string;
  itinerary?: string;
  created_at: string;
}

/**
 * Fetch all quotes with itinerary data
 */
export const fetchQuoteItineraries = async (): Promise<QuoteItinerary[]> => {
  try {
    const supabase = await getQuoteClient();

    const { data, error } = await supabase
      .from('quotes')
      .select('id, package_name, customer_name, destination, trip_duration, itinerary, created_at')
      .not('itinerary', 'is', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching quote itineraries:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch quote itineraries:', error);
    throw error;
  }
};

/**
 * Fetch all itinerary templates
 */
export const fetchItineraryTemplates = async (): Promise<ItineraryTemplate[]> => {
  try {
    const supabase = await getQuoteClient();

    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching itinerary templates:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch itinerary templates:', error);
    throw error;
  }
};

/**
 * Fetch a single itinerary template by ID
 */
export const fetchItineraryTemplate = async (id: string): Promise<ItineraryTemplate | null> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching itinerary template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to fetch itinerary template:', error);
    throw error;
  }
};

/**
 * Create a new itinerary template
 */
export const createItineraryTemplate = async (template: CreateItineraryTemplate): Promise<ItineraryTemplate> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .insert([template])
      .select()
      .single();

    if (error) {
      console.error('Error creating itinerary template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to create itinerary template:', error);
    throw error;
  }
};

/**
 * Update an existing itinerary template
 */
export const updateItineraryTemplate = async (
  id: string, 
  updates: UpdateItineraryTemplate
): Promise<ItineraryTemplate> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating itinerary template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to update itinerary template:', error);
    throw error;
  }
};

/**
 * Delete an itinerary template
 */
export const deleteItineraryTemplate = async (id: string): Promise<void> => {
  try {
    const supabase = await getQuoteClient();
    
    const { error } = await supabase
      .from('itinerary_templates')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting itinerary template:', error);
      throw error;
    }
  } catch (error) {
    console.error('Failed to delete itinerary template:', error);
    throw error;
  }
};

/**
 * Fetch unique destinations from quotes table
 */
export const fetchDestinations = async (): Promise<string[]> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('quotes')
      .select('destination')
      .not('destination', 'is', null);

    if (error) {
      console.error('Error fetching destinations:', error);
      // Return fallback destinations if fetch fails
      return [
        'Kerala - Gods Own Country',
        'Goa - Beach Paradise', 
        'Rajasthan - Royal Heritage',
        'Himachal - Mountain Paradise',
        'Karnataka - Cultural Haven',
        'Andaman - Island Paradise',
        'Sikkim - Northeast Wonder',
        'Kashmir - Heaven on Earth'
      ];
    }
    
    const uniqueDestinations = [...new Set(data?.map(item => item.destination) || [])]
      .filter(dest => dest && dest.trim() !== '')
      .sort();
    
    return uniqueDestinations;
  } catch (error) {
    console.error('Failed to fetch destinations:', error);
    // Return fallback destinations if fetch fails
    return [
    'Manali', 'Ooty', 'Munnar', 'Andaman', 'Kodaikanal', 'Coorg', 'Alleppey',
    'Kochi', 'Shimla', 'Yelagiri', 'Wayanad', 'Meghalaya', 'Darjeeling', 'Sikkim',
    'Delhi', 'Agra', 'Pondicherry', 'Madurai', 'Rameswaram', 'Ladakh', 'Mysore',
    'Bali', 'Maldives', 'Europe', 'Thailand', 'Singapore', 'Abu Dhabi', 'Vietnam',
    'Dubai', 'Australia'
    ];
  }
};



/**
 * Convert quote itinerary to template
 */
export const convertQuoteToTemplate = async (quote: QuoteItinerary): Promise<ItineraryTemplate> => {
  try {
    const supabase = await getQuoteClient();

    // Format name as "Destination-NightsNDays"
    const formattedName = `${quote.destination}-${quote.trip_duration?.replace('/', '&') || 'Custom'}`;

    const templateData = {
      name: formattedName,
      content: quote.itinerary || ''
    };

    const { data, error } = await supabase
      .from('itinerary_templates')
      .insert([templateData])
      .select()
      .single();

    if (error) {
      console.error('Error converting quote to template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to convert quote to template:', error);
    throw error;
  }
};

/**
 * Search quote itineraries
 */
export const searchQuoteItineraries = async (searchTerm: string): Promise<QuoteItinerary[]> => {
  try {
    const supabase = await getQuoteClient();

    const { data, error } = await supabase
      .from('quotes')
      .select('id, package_name, customer_name, destination, trip_duration, itinerary, created_at')
      .not('itinerary', 'is', null)
      .or(`package_name.ilike.%${searchTerm}%,customer_name.ilike.%${searchTerm}%,destination.ilike.%${searchTerm}%,trip_duration.ilike.%${searchTerm}%`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching quote itineraries:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to search quote itineraries:', error);
    throw error;
  }
};

/**
 * Search itinerary templates by name
 */
export const searchItineraryTemplates = async (searchTerm: string): Promise<ItineraryTemplate[]> => {
  try {
    const supabase = await getQuoteClient();

    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .ilike('name', `%${searchTerm}%`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching itinerary templates:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to search itinerary templates:', error);
    throw error;
  }
};
