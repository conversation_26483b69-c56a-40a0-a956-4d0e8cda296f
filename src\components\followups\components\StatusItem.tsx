import React from 'react';
import StatusBadge from './StatusBadge';

interface StatusItemProps {
  label: string;
  value: string;
  isEditing: boolean;
  options: string[];
  onChange: (value: string) => void;
  amount?: number | null;
  onAmountChange?: (amount: number) => void;
  showAmountInput?: boolean;
}

const StatusItem: React.FC<StatusItemProps> = ({
  label,
  value,
  isEditing,
  options,
  onChange,
  amount,
  onAmountChange,
  showAmountInput
}) => (
  <div className="p-5 border border-gray-200 rounded-xl bg-gradient-to-br from-gray-50 to-white shadow-sm hover:shadow-md transition-shadow duration-200">
    <p className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
      <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
      {label}
    </p>
    {isEditing ? (
      <div className="space-y-4">
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-2">Status</label>
          <select
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm transition-all duration-200"
          >
            {options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
          </select>
        </div>
        {showAmountInput && onAmountChange && (
          <div>
            <label className="block text-xs font-medium text-gray-600 mb-2">Amount (₹)</label>
            <input
              type="number"
              placeholder="Enter amount"
              value={amount || ''}
              onChange={(e) => onAmountChange(parseFloat(e.target.value) || 0)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary shadow-sm transition-all duration-200"
            />
          </div>
        )}
      </div>
    ) : (
      <div className="space-y-3">
        <StatusBadge status={value} />
        {showAmountInput && (
          <div className="bg-white p-3 rounded-lg border border-gray-100">
            <span className="text-xs text-gray-500 block">Amount</span>
            <p className="text-xl font-bold text-gray-800">₹{amount?.toLocaleString() || 'N/A'}</p>
          </div>
        )}
      </div>
    )}
  </div>
);

export default StatusItem;
