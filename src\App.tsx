import React, { useState, useEffect } from 'react';
import './index.css'; // Ensure CSS is loaded
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Login from './pages/Login';
import Signup from './pages/Signup';
import ProtectedRoute from './components/ProtectedRoute';
import Dashboard from './pages/Dashboard';
import LeadEntry from './pages/LeadEntry';
import LeadsKanban from './pages/LeadsKanban';
import LeadsTable from './pages/LeadsTable';
import Quotes from './pages/Quotes';
import ItineraryManagement from './pages/ItineraryManagement';
import InvoiceManagement from './pages/InvoiceManagement';
import AppLayout from './components/AppLayout';
import PrepaidEMIManagement from './components/PrepaidEMIManagement';
import FamilyTypeManagement from './components/FamilyTypeManagement';
import PrepaidEmiCalculator from './quotes/Tabs/PrepaidEmiCalculator';
import CustomerManagement from './pages/CustomerManagement';
import TravelInquiry from './pages/TravelInquiry';
import PromoGenerator from './pages/PromoGenerator';
import SharedQuote from './pages/SharedQuote';
import ErrorBoundary from './components/ErrorBoundary';
import Followups from './pages/Followups';

// Redirect root based on authentication state
function RootRedirect() {
  const { isAuthenticated, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [loadingTime, setLoadingTime] = useState<number>(0);

  useEffect(() => {
    let intervalId: number;

    // Only start the timer if we're in the loading state
    if (isLoading) {
      // Create a loading timer that increments every second
      intervalId = window.setInterval(() => {
        setLoadingTime(prevTime => {
          const newTime = prevTime + 1;
          // After 15 seconds, show a friendly message
          if (newTime === 15) {
            setError('Still connecting to Supabase...');
          }
          // After 30 seconds, show a more detailed message
          if (newTime === 30) {
            setError('Connection is taking longer than usual. The database might be waking up from sleep mode.');
          }
          return newTime;
        });
      }, 1000) as unknown as number;
    } else {
      // Reset when not loading
      setLoadingTime(0);
      setError(null);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isLoading]);

  // Handler for manual retry
  const handleRetry = () => {
    setError(null);
    setLoadingTime(0);
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <div className="mb-4">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
          <p className="text-lg">Loading session{loadingTime > 5 ? ` (${loadingTime}s)` : '...'}</p>
          {error && (
            <div className="mt-4 p-4 bg-blue-50 text-blue-700 rounded-lg w-full max-w-sm mx-auto">
              <p>{error}</p>
              {loadingTime > 20 && (
                <button
                  onClick={handleRetry}
                  className="mt-3 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                >
                  Retry Connection
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  return <Navigate to={isAuthenticated ? '/dashboard' : '/login'} replace />;
}

// Fallback component for auth errors
function AuthErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Handle unhandled errors related to auth
    const handler = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
      if (event.error?.message?.includes('Supabase')) {
        setHasError(true);
      }
    };

    window.addEventListener('error', handler);
    return () => window.removeEventListener('error', handler);
  }, []);

  if (hasError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-50 p-6 rounded max-w-md">
          <h2 className="text-xl font-semibold mb-2">Authentication Error</h2>
          <p>There was a problem setting up the authentication service.</p>
          <p className="mt-2">
            Please check your Supabase configuration and ensure environment variables are correctly set.
          </p>
          <button
            className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
            onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

function App() {
  return (
    <Router>
      <AuthErrorBoundary>
        <AuthProvider>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            {/* Public routes */}
            <Route path="/travel-inquiry" element={<TravelInquiry />} />
            <Route
              path="/shared-quote/:quoteId"
              element={
                <ErrorBoundary>
                  <React.Suspense fallback={<div>Loading...</div>}>
                    <SharedQuote />
                  </React.Suspense>
                </ErrorBoundary>
              }
            />
            <Route element={<ProtectedRoute />}>
              <Route
                path="/promo-generator"
                element={
                  <AppLayout>
                    <PromoGenerator />
                  </AppLayout>
                }
              />
              <Route
                path="/dashboard"
                element={
                  <AppLayout>
                    <Dashboard />
                  </AppLayout>
                }
              />
              <Route
                path="/leads"
                element={
                  <AppLayout>
                    <LeadsKanban />
                  </AppLayout>
                }
              />
              <Route
                path="/leads/table"
                element={
                  <AppLayout>
                    <LeadsTable />
                  </AppLayout>
                }
              />
              <Route
                path="/leads/new"
                element={
                  <AppLayout>
                    <LeadEntry />
                  </AppLayout>
                }
              />
              <Route
                path="/quotes"
                element={
                  <AppLayout>
                    <Quotes />
                  </AppLayout>
                }
              />
              <Route
                path="/quotes/new"
                element={
                  <AppLayout>
                    <Quotes />
                  </AppLayout>
                }
              />
              <Route
                path="/saved-quotes"
                element={
                  <AppLayout>
                    <Quotes />
                  </AppLayout>
                }
              />
              <Route
                path="/itinerary-management"
                element={
                  <AppLayout>
                    <ItineraryManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/invoice-management"
                element={
                  <AppLayout>
                    <InvoiceManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/customers"
                element={
                  <AppLayout>
                    <CustomerManagement />
                  </AppLayout>
                }
              />
              {/* EMI Management Routes */}
              <Route
                path="/emi/dashboard"
                element={
                  <AppLayout>
                    <PrepaidEMIManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/emi/transactions"
                element={
                  <AppLayout>
                    <PrepaidEMIManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/emi/calculator"
                element={
                  <AppLayout>
                    <PrepaidEmiCalculator />
                  </AppLayout>
                }
              />
              <Route
                path="/family-types"
                element={
                  <AppLayout>
                    <FamilyTypeManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/followups"
                element={
                  <AppLayout>
                    <Followups />
                  </AppLayout>
                }
              />
              <Route
                path="/followups/create"
                element={
                  <AppLayout>
                    <Followups />
                  </AppLayout>
                }
              />
              <Route
                path="/followups/select-quote"
                element={
                  <AppLayout>
                    <Followups />
                  </AppLayout>
                }
              />
              <Route
                path="/followups/:id"
                element={
                  <AppLayout>
                    <Followups />
                  </AppLayout>
                }
              />
            </Route>
            <Route path="/" element={<RootRedirect />} />
          </Routes>
        </AuthProvider>
      </AuthErrorBoundary>
    </Router>
  );
}

export default App;
