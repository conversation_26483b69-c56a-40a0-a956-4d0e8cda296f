import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { User, IndianRupee } from 'lucide-react';
import { getQuoteClient } from '../../lib/supabaseManager';
import { HotelRow, Costs } from './types';

const CreateFollowup: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedQuoteId, setSelectedQuoteId] = useState<string>('');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');

  const [hotelRows, setHotelRows] = useState<HotelRow[]>([]);
  const [costs, setCosts] = useState<Costs>({
    transportation: 0,
    cab_sightseeing: 0,
    train_cost: 0,
    ferry_cost: 0,
    marketing: 0,
    add_on_activity: 0,
  });
  const [supabase, setSupabase] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const desiredCosts: (keyof Costs)[] = [
    'transportation',
    'cab_sightseeing',
    'train_cost',
    'ferry_cost',
    'marketing',
    'add_on_activity',
  ];

  useEffect(() => {
    const initSupabase = async () => {
      try {
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
        setError('Could not connect to the database.');
      }
    };
    initSupabase();
  }, []);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const quoteId = params.get('quoteId');
    if (quoteId) {
      setSelectedQuoteId(quoteId);
    }
  }, [location.search]);

  useEffect(() => {
    if (supabase && selectedQuoteId) {
      const fetchQuoteDetails = async () => {
        try {
          const { data, error } = await supabase
            .from('quotes')
            .select('*, hotel_rows(*), costs(*)')
            .eq('id', selectedQuoteId)
            .single();

          if (error) throw error;

          if (data) {
            setCustomerName(data.customer_name || '');
            setCustomerPhone(data.customer_phone || '');
            setCustomerEmail(data.customer_email || '');
            setHotelRows(data.hotel_rows || []);
            setCosts(data.costs ? data.costs[0] : {
              transportation: 0,
              cab_sightseeing: 0,
              train_cost: 0,
              ferry_cost: 0,
              marketing: 0,
            });

          }
        } catch (err) {
          setError(err instanceof Error ? `Error fetching quote details: ${err.message}` : 'An unknown error occurred.');
        }
      };
      fetchQuoteDetails();
    }
  }, [supabase, selectedQuoteId]);

  const handleHotelRowChange = (index: number, field: keyof HotelRow, value: string) => {
    const updatedRows = [...hotelRows];
    if (field === 'stay_price' || field === 'price') {
      updatedRows[index][field] = parseFloat(value) || 0;
    } else {
      updatedRows[index][field] = value;
    }
    setHotelRows(updatedRows);
  };

  const handleCostChange = (field: keyof Costs, value: string) => {
    setCosts(prev => ({ ...prev, [field]: parseFloat(value) || 0 }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    if (!supabase) {
      setError('Database connection not available.');
      setIsSubmitting(false);
      return;
    }

    try {
      const hotelDetails = hotelRows.map(h => ({
        name: h.hotel_name,
        price: h.stay_price,
        advance: 0, // Default advance to 0
        full_amount_paid: 0,
        status: 'pending' // Default status
      }));

      const hotel_full_amount = hotelDetails.reduce((sum, hotel) => sum + hotel.price, 0);

      // Calculate total cost as sum of all amounts
      const totalCost = hotel_full_amount + (costs.cab_sightseeing || 0) + (costs.train_cost || 0) + ((costs.transportation || 0) + (costs.ferry_cost || 0)) + (costs.marketing || 0) + (costs.add_on_activity || 0) + (costs.commission_amount || costs.commission || 0) + (costs.gst_amount || costs.gst || 0);

      // Calculate initial profit (at creation time, profit = totalCost - commissionAmount - gstAmount)
      const commissionAmount = costs.commission_amount || costs.commission || 0;
      const gstAmount = costs.gst_amount || costs.gst || 0;
      const initialProfit = Math.round(totalCost + commissionAmount - gstAmount);

      // Create the followup entry
      const { error: insertError } = await supabase.from('followups').insert({
        quote_id: selectedQuoteId || null,
        customer_name: customerName,
        customer_phone: customerPhone,
        customer_email: customerEmail,

        hotel_status: 'pending', // Overall status, can be deprecated later
        cab_status: 'pending',
        flight_train_status: 'pending',
        transportation_status: 'pending',
        hotel_details: JSON.stringify(hotelDetails),
        hotel_full_amount: hotel_full_amount,
        hotel_advance_amount: 0,
        hotel_full_amount_paid: 0,
        transportation_quote_amount: (costs.transportation || 0) + (costs.ferry_cost || 0),
        transportation_full_amount_paid: 0,
        cab_full_amount: costs.cab_sightseeing,
        cab_advance_amount: 0,
        cab_full_amount_paid: 0,
        flight_train_quote_amount: costs.train_cost || 0,
        flight_train_full_amount_paid: 0,
        add_on_amount: costs.add_on_activity || 0,
        subtotal: costs.subtotal || 0,
        commission_amount: costs.commission_amount || costs.commission || 0,
        gst_amount: costs.gst_amount || costs.gst || 0,
        marketing_amount: costs.marketing || 0,
        total_cost: totalCost,
        total_paid: 0, // Initialize with 0, will be updated as payments are made
        profit: initialProfit
      });

      if (insertError) {
        console.error('Insert error:', insertError);
        throw insertError;
      }

      navigate('/followups');
    } catch (err) {
      setError(err instanceof Error ? `Error: ${err.message}` : 'An unknown error occurred.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDisplayLabel = (key: string) => {
    switch (key) {
      case 'train_cost':
        return 'Train / Flight Costs';
      case 'transportation':
        return 'Transportation / Bus Costs';
      case 'cab_sightseeing':
        return 'Cab Sightseeing Cost';
      case 'ferry_cost':
        return 'Ferry Cost';
      case 'marketing':
        return 'Marketing Cost';
        case 'add_on_activity':
        return 'Add-on Activity Cost'
      default:
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 sm:mb-6">Create New Follow-up</h1>
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-8 max-w-3xl mx-auto">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
            {/* Customer Name (Read-only) */}
            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-2" />Customer
              </label>
              <input
                type="text"
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
              />
            </div>

            {/* Customer Phone (Read-only) */}
            <div>
              <label htmlFor="customerPhone" className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-2" />Phone
              </label>
              <input
                type="text"
                id="customerPhone"
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
              />
            </div>

            {/* Customer Email (Read-only) */}
            <div>
              <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-2" />Email
              </label>
              <input
                type="text"
                id="customerEmail"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
              />
            </div>


          </div>

          {/* Costs Information Display */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Cost Breakdown</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              {desiredCosts.map((key) => (
                <div key={key}>
                  <label htmlFor={key} className="block text-sm font-medium text-gray-700 mb-2">
                    <IndianRupee className="w-4 h-4 inline mr-2" />
                    {getDisplayLabel(key)}
                  </label>
                  <input
                    type="number"
                    id={key}
                    value={costs[key] || ''}
                    onChange={(e) => handleCostChange(key, e.target.value)}
                    className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Hotel Rows */}
          <div className="mb-4 sm:mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Hotel Details</h3>
            <div className="space-y-4">
              {hotelRows.map((row, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 p-3 sm:p-4 border border-gray-200 rounded-lg">
                  <div>
                    <label htmlFor={`hotelName-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                      Hotel Name
                    </label>
                    <input
                      type="text"
                      id={`hotelName-${index}`}
                      value={row.hotel_name}
                      onChange={(e) => handleHotelRowChange(index, 'hotel_name', e.target.value)}
                      className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                    />
                  </div>
                  <div>
                    <label htmlFor={`hotelPrice-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                      Price
                    </label>
                    <input
                      type="number"
                      id={`hotelPrice-${index}`}
                      value={row.stay_price}
                      onChange={(e) => handleHotelRowChange(index, 'stay_price', e.target.value)}
                      className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {error && <p className="text-red-600 bg-red-50 p-2 sm:p-3 rounded-lg text-sm mb-4">{error}</p>}

          <div className="flex justify-end gap-3 sm:gap-4 mt-6 sm:mt-8">
            <button
              type="button"
              onClick={() => navigate('/followups')}
              className="bg-gray-200 text-gray-800 px-4 sm:px-6 py-2 rounded-lg hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary text-white px-4 sm:px-6 py-2 rounded-lg hover:bg-primary-dark"
            >
              {isSubmitting ? 'Submitting...' : 'Create Follow-up'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateFollowup;
